{"cells": [{"cell_type": "code", "execution_count": 2, "id": "7aed5694", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pathlib as pl"]}, {"cell_type": "code", "execution_count": 6, "id": "4dd89b05", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133,139,140,141) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (139,140,141) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133,139,140,141) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (139,140,141) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (139,140,141) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133,139,140,141) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133,139,140,141) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133,139,140,141) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133,139,140,141) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (139,140,141) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (139,140,141) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133,139,140,141) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133,139,140,141) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (139,140,141) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (112) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (55) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19,55) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (55) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19,55) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19,55) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (55) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (112) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (112) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19,55,112) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (47,123,124,125,128,129,130,133) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (47) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (47) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (47) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (47) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (123,124,125,128,129,130,133) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (47) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27368\\2147435978.py:8: DtypeWarning: Columns (19) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n"]}], "source": ["# Make sure jupyter lab's current directory is the project root\n", "DATA_PATH = pl.Path.cwd() / \"data\" / \"Lending Club loan data\" / \"loan.csv\"\n", "\n", "\n", "chunk_size = 10000\n", "chunks = []\n", "\n", "for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):\n", "    chunks.append(chunk)\n", "\n", "df = pd.concat(chunks, ignore_index=True)"]}, {"cell_type": "code", "execution_count": null, "id": "1efa294e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Column 19 - 'desc':\n", "Unique values: [nan ' '\n", " \"I currently have a loan out with CashCall. The interest rate is 96%! At the time I took out the loan, it helped with a family crisis, but now the interest is crazy to be paying. I'd rather be paying my $200 a month to pay down a loan rather than just the interest.   Also, the remainder of my debt has interest rates ranging from 20 - 24%. This includes credit cards, a student loan, and some personal loans. So, I would like to consolidate the rest of my debt and pay a lower interest rate of 15%.  By doing this, I could lower my interest and my monthly payments and save at least $4000 over the next few years.  I do have a bankruptcy that was discharged 3 years ago due to having $40,000 in debt from an uninsured hospital stay. However, I've not missed a payment on anything since.  My credit report does from time time to time relist things as collections and then I have to dispute them as included in bankruptcy and they go back to being listed that way. I'm not sure why the status gets messed up sometimes.   Thank you for your time and consideration! \"\n", " ... 'I need to pay $2,100 for fixing my Volvo :)  Any help appreciated!'\n", " \"Hi,   I'm buying  a used car. Anybody on facebook wants to finance me?   Thanks\"\n", " 'I need to make several improvements around the house - fix garage, fix back fencing, and misc other.']\n", "+==================================================+\n", "Column 47 - 'next_pymnt_d':\n", "Unique values: ['Mar-2019' nan 'Feb-2019' 'Apr-2019' 'Dec-2018' 'Sep-2018' 'Aug-2018'\n", " 'Feb-2018' 'Jan-2016' 'Sep-2013' 'Feb-2016' 'Feb-2014' 'May-2014'\n", " 'Jun-2013' 'Mar-2012' 'Apr-2012' 'May-2013' 'Aug-2012' 'Aug-2013'\n", " 'Jun-2012' 'Nov-2013' 'Feb-2012' 'Oct-2011' 'Jan-2013' 'Jan-2014'\n", " 'Jul-2013' 'Jul-2015' 'Jan-2012' 'Dec-2012' 'Jun-2011' 'Feb-2013'\n", " 'Nov-2011' 'Nov-2012' 'Dec-2011' 'Aug-2011' 'Sep-2011' 'Apr-2011'\n", " 'Mar-2014' 'Apr-2013' 'Mar-2011' 'Jul-2012' 'Aug-2014' 'Oct-2013'\n", " 'Sep-2012' 'May-2012' 'Apr-2015' 'Jul-2011' 'Dec-2015' 'Dec-2013'\n", " 'Jan-2011' 'Oct-2012' 'Nov-2014' 'Mar-2013' 'Aug-2015' 'Feb-2015'\n", " 'May-2015' 'Jul-2014' 'Nov-2015' 'Sep-2014' 'Oct-2015' 'May-2011'\n", " 'Feb-2011' 'Dec-2014' 'Jun-2015' 'Apr-2014' 'Jan-2015' 'Sep-2015'\n", " 'Jun-2014' 'Nov-2010' 'Oct-2010' 'Dec-2010' 'Mar-2015' 'Oct-2014'\n", " 'Jul-2010' 'Sep-2010' 'May-2010' 'Aug-2010' 'Mar-2010' 'Jun-2010'\n", " 'Apr-2010' 'Feb-2010' 'Dec-2009' 'Nov-2009' 'Oct-2009' 'Jan-2010'\n", " 'Sep-2009' 'Jun-2009' 'Aug-2009' 'Jul-2009' 'May-2009' 'Apr-2009'\n", " 'Jan-2009' 'Oct-2008' 'Feb-2009' 'Nov-2008' 'Sep-2008' 'Mar-2009'\n", " 'Dec-2008' 'Aug-2008' 'Jun-2008' 'Jul-2008' 'Apr-2008' 'May-2008'\n", " 'Feb-2008' 'Jan-2008' 'Mar-2008']\n", "+==================================================+\n", "Column 123 - 'hardship_type':\n", "Unique values: [nan 'INTEREST ONLY-3 MONTHS DEFERRAL']\n", "+==================================================+\n", "Column 124 - 'hardship_reason':\n", "Unique values: [nan 'UNEMPLOYMENT' 'NATURAL_DISASTER' 'EXCESSIVE_OBLIGATIONS' 'MEDICAL'\n", " 'INCOME_CURTAILMENT' 'DISABILITY' 'REDUCED_HOURS' 'FAMILY_DEATH'\n", " 'DIVORCE']\n", "+==================================================+\n", "Column 125 - 'hardship_status':\n", "Unique values: [nan 'ACTIVE' 'COMPLETED' 'BROKEN']\n", "+==================================================+\n", "Column 128 - 'hardship_start_date':\n", "Unique values: [nan 'Feb-2019' 'Oct-2018' 'Nov-2018' 'Sep-2018' 'Jan-2019' 'Dec-2018'\n", " 'Aug-2018' 'Jul-2018' 'May-2018' 'Sep-2017' 'Feb-2018' 'Dec-2017'\n", " 'Apr-2018' 'Aug-2017' 'Jan-2018' 'Mar-2018' 'Jun-2018' 'Oct-2017'\n", " 'Nov-2017' 'Jul-2017' 'Jun-2017' 'May-2017' 'Feb-2017' 'Apr-2017'\n", " 'Jan-2017' 'Mar-2017']\n", "+==================================================+\n", "Column 129 - 'hardship_end_date':\n", "Unique values: [nan 'Apr-2019' 'Dec-2018' 'Jan-2019' 'Feb-2019' 'Oct-2018' 'May-2019'\n", " 'Mar-2019' 'Nov-2018' 'Aug-2018' 'Dec-2017' 'May-2018' 'Nov-2017'\n", " 'Mar-2018' 'Jul-2018' 'Apr-2018' 'Jun-2018' 'Sep-2017' 'Sep-2018'\n", " 'Oct-2017' 'Jan-2018' 'Feb-2018' 'Aug-2017' 'Jul-2017' 'Jun-2017'\n", " 'May-2017' 'Apr-2017' 'Mar-2017']\n", "+==================================================+\n", "Column 130 - 'payment_plan_start_date':\n", "Unique values: [nan 'Feb-2019' 'Oct-2018' 'Nov-2018' 'Dec-2018' 'Jan-2019' 'Sep-2018'\n", " 'Mar-2019' 'Aug-2018' 'Jun-2018' 'Sep-2017' 'Oct-2017' 'Feb-2018'\n", " 'Dec-2017' 'May-2018' 'Aug-2017' 'Jan-2018' 'Mar-2018' 'Apr-2018'\n", " 'Nov-2017' 'Jul-2018' 'Jul-2017' 'Jun-2017' 'May-2017' 'Mar-2017'\n", " 'Feb-2017' 'Apr-2017']\n", "+==================================================+\n", "Column 133 - 'hardship_loan_status':\n", "Unique values: [nan 'Late (16-30 days)' 'Issued' 'Current' 'Late (31-120 days)'\n", " 'In Grace Period']\n", "+==================================================+\n", "Column 139 - 'debt_settlement_flag_date':\n", "Unique values: [nan 'Feb-2019' 'Dec-2018' 'Jan-2019' 'Nov-2018' 'Oct-2018' 'Sep-2018'\n", " 'Aug-2018' 'Jul-2018' 'Jun-2018' 'Apr-2018' 'Dec-2017' 'Feb-2018'\n", " 'Sep-2017' 'May-2018' 'Mar-2018' 'Nov-2017' 'Jan-2018' 'Jun-2017'\n", " 'May-2017' 'Jul-2017' 'Aug-2017' 'Oct-2017' 'Apr-2017' 'Mar-2017'\n", " 'Jan-2017' 'Feb-2017' 'Dec-2016' 'Nov-2016' 'Aug-2016' 'Sep-2016'\n", " 'Jul-2016' 'Oct-2016' 'Jun-2016' 'May-2016' 'Apr-2016' 'Mar-2016'\n", " 'Feb-2016' 'Jan-2016' 'Dec-2015' 'Sep-2015' 'Oct-2015' 'Aug-2015'\n", " 'Jun-2015' 'Feb-2015' 'Nov-2015' 'May-2015' 'Nov-2014' 'Mar-2015'\n", " 'Apr-2015' 'Jul-2015' 'Jan-2015' 'Dec-2014' 'May-2014' 'Sep-2014'\n", " 'Jul-2014' 'Apr-2014' 'Jun-2014' 'Oct-2014' 'Aug-2014' 'Aug-2013'\n", " 'Mar-2014' 'Jan-2014' 'Nov-2013' 'Oct-2013' 'Feb-2014' 'Sep-2013'\n", " 'Mar-2013' 'Jun-2013' 'Nov-2012' 'Dec-2013' 'Oct-2012' 'Jun-2012'\n", " 'Apr-2013' 'Jul-2013' 'Feb-2012' 'Sep-2012' 'Feb-2011' 'Dec-2012'\n", " 'Oct-2011' 'Feb-2013' 'Nov-2011' 'Feb-2010']\n", "+==================================================+\n", "Column 140 - 'settlement_status':\n", "Unique values: [nan 'ACTIVE' 'COMPLETE' 'BROKEN']\n", "+==================================================+\n", "Column 141 - 'settlement_date':\n", "Unique values: [nan 'Feb-2019' 'Dec-2018' 'Jan-2019' 'Nov-2018' 'Oct-2018' 'Sep-2018'\n", " 'Aug-2018' 'Jul-2018' 'Jun-2018' 'Apr-2018' 'Aug-2017' 'Nov-2017'\n", " 'Jan-2018' 'Jun-2017' 'May-2018' 'Jul-2017' 'Feb-2018' 'Mar-2018'\n", " 'Dec-2017' 'May-2017' 'Sep-2017' 'Oct-2017' 'Feb-2017' 'Jan-2017'\n", " 'Mar-2017' 'Apr-2017' 'Dec-2016' 'Nov-2016' 'Oct-2016' 'Sep-2016'\n", " 'Aug-2016' 'Jul-2016' 'Jun-2016' 'Apr-2016' 'May-2016' 'Mar-2016'\n", " 'Feb-2016' 'Jan-2016' 'Dec-2015' 'Oct-2015' 'Sep-2015' 'Aug-2015'\n", " 'Nov-2015' 'Jun-2015' 'Jul-2015' 'May-2015' 'Sep-2014' 'Apr-2015'\n", " 'Oct-2014' 'Jan-2015' 'Mar-2015' 'Feb-2015' 'Nov-2014' 'Aug-2014'\n", " 'Dec-2014' 'Jul-2014' 'Apr-2014' 'Jun-2014' 'Mar-2014' 'May-2014'\n", " 'Feb-2014' 'Jan-2014' 'Nov-2013' 'Oct-2013' 'Dec-2013' 'Jul-2013'\n", " 'Aug-2013' 'Sep-2013' 'Jun-2013' 'Feb-2013' 'Mar-2013' 'May-2013'\n", " 'Nov-2012' 'Dec-2012' 'Apr-2013' 'Oct-2012' 'May-2012' 'Jun-2012'\n", " 'Aug-2012' 'Jan-2012' 'Sep-2012' 'Oct-2011' 'Feb-2012' 'Jul-2012'\n", " 'Feb-2011' 'Jul-2011' 'Mar-2012' 'Feb-2010' 'Mar-2009']\n", "+==================================================+\n"]}], "source": ["column_indices = [19, 47, 123, 124, 125, 128, 129, 130, 133, 139, 140, 141]\n", "\n", "# Iterate through the indices and print the column name and unique values\n", "for idx in column_indices:\n", "    if idx < len(df.columns):\n", "        column_name = df.columns[idx]\n", "        unique_values = df.iloc[:, idx].unique()\n", "        print(f\"Column {idx} - '{column_name}':\")\n", "        print(f\"Unique values: {unique_values}\")\n", "        print(\"+\" + \"=\"*50 + \"+\")\n", "    else:\n", "        print(f\"Column {idx}: Index out of range\")"]}, {"cell_type": "code", "execution_count": 32, "id": "96291847", "metadata": {}, "outputs": [{"data": {"text/plain": ["(2260668, 145)"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["df.shape"]}, {"cell_type": "code", "execution_count": 53, "id": "5cf8f371", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Number of unique values: 2\n", "Unique values in 'hardship_flag': ['N' 'Y']\n", "--------------------------------------------------\n", "Count of each unique value:\n", "N: 2259783\n", "Y: 885\n", "+==================================================+\n", "Number of unique values: 2\n", "Unique values in 'hardship_type': [nan 'INTEREST ONLY-3 MONTHS DEFERRAL']\n", "--------------------------------------------------\n", "Count of each unique value:\n", "nan: 0\n", "INTEREST ONLY-3 MONTHS DEFERRAL: 10613\n", "+==================================================+\n", "Number of unique values: 10\n", "Unique values in 'hardship_reason': [nan 'UNEMPLOYMENT' 'NATURAL_DISASTER' 'EXCESSIVE_OBLIGATIONS' 'MEDICAL'\n", " 'INCOME_CURTAILMENT' 'DISABILITY' 'REDUCED_HOURS' 'FAMILY_DEATH'\n", " 'DIVORCE']\n", "--------------------------------------------------\n", "Count of each unique value:\n", "nan: 0\n", "UNEMPLOYMENT: 1834\n", "NATURAL_DISASTER: 2965\n", "EXCESSIVE_OBLIGATIONS: 2079\n", "MEDICAL: 1249\n", "INCOME_CURTAILMENT: 1279\n", "DISABILITY: 154\n", "REDUCED_HOURS: 629\n", "FAMILY_DEATH: 206\n", "DIVORCE: 218\n", "+==================================================+\n", "Number of unique values: 4\n", "Unique values in 'hardship_status': [nan 'ACTIVE' 'COMPLETED' 'BROKEN']\n", "--------------------------------------------------\n", "Count of each unique value:\n", "nan: 0\n", "ACTIVE: 885\n", "COMPLETED: 7541\n", "BROKEN: 2187\n", "+==================================================+\n", "Number of unique values: 2\n", "Unique values in 'deferral_term': [nan  3.]\n", "--------------------------------------------------\n", "Count of each unique value:\n", "nan: 0\n", "3.0: 10613\n", "+==================================================+\n", "Number of unique values: 8951\n", "Unique values in 'hardship_amount': [   nan 378.39 203.67 ... 109.91 210.59 295.13]\n", "--------------------------------------------------\n", "Count of each unique value:\n", "nan: 0\n", "378.39: 1\n", "203.67: 1\n", "378.9: 1\n", "126.27: 1\n", "119.59: 1\n", "243.56: 1\n", "159.94: 1\n", "241.9: 1\n", "134.81: 1\n", "194.82: 4\n", "55.04: 1\n", "215.7: 1\n", "248.46: 2\n", "109.26: 2\n", "22.69: 2\n", "218.13: 1\n", "57.5: 1\n", "12.26: 2\n", "94.78: 1\n", "5.95: 1\n", "114.36: 2\n", "55.58: 1\n", "171.04: 1\n", "36.63: 1\n", "219.99: 1\n", "118.5: 1\n", "96.14: 1\n", "242.52: 1\n", "175.66: 1\n", "467.15: 1\n", "399.53: 1\n", "93.46: 1\n", "478.38: 1\n", "196.12: 2\n", "289.13: 1\n", "290.52: 1\n", "114.38: 2\n", "116.91: 3\n", "121.43: 1\n", "183.65: 1\n", "90.36: 1\n", "265.36: 1\n", "302.84: 1\n", "326.6: 1\n", "231.94: 1\n", "85.03: 1\n", "472.35: 1\n", "119.04: 1\n", "104.74: 1\n", "340.41: 1\n", "120.72: 2\n", "147.81: 1\n", "110.19: 1\n", "51.08: 1\n", "53.11: 1\n", "75.4: 2\n", "413.99: 1\n", "293.41: 1\n", "170.05: 1\n", "133.89: 1\n", "104.6: 1\n", "158.91: 3\n", "94.22: 2\n", "10.55: 1\n", "63.85: 2\n", "252.97: 1\n", "359.38: 1\n", "81.03: 1\n", "283.79: 1\n", "112.7: 2\n", "194.04: 2\n", "394.51: 1\n", "283.73: 1\n", "55.55: 2\n", "65.44: 2\n", "247.68: 1\n", "26.94: 1\n", "63.88: 4\n", "189.69: 2\n", "200.66: 1\n", "204.25: 1\n", "136.12: 1\n", "29.69: 3\n", "261.97: 1\n", "122.88: 1\n", "25.6: 2\n", "575.28: 1\n", "247.33: 1\n", "335.22: 1\n", "126.8: 2\n", "80.77: 3\n", "129.01: 1\n", "126.53: 2\n", "162.06: 2\n", "216.82: 1\n", "75.26: 1\n", "96.37: 2\n", "74.89: 1\n", "54.62: 3\n", "66.81: 1\n", "110.79: 1\n", "61.1: 1\n", "51.2: 4\n", "718.25: 1\n", "39.55: 2\n", "325.51: 1\n", "335.18: 2\n", "323.32: 1\n", "86.24: 2\n", "217.78: 1\n", "57.85: 2\n", "317.13: 1\n", "497.11: 1\n", "130.36: 1\n", "266.11: 2\n", "41.26: 1\n", "129.28: 1\n", "248.87: 1\n", "300.23: 1\n", "432.66: 1\n", "84.91: 1\n", "113.93: 2\n", "247.22: 1\n", "225.42: 1\n", "163.65: 2\n", "837.68: 1\n", "62.18: 1\n", "89.74: 2\n", "845.22: 1\n", "89.49: 2\n", "140.41: 2\n", "218.05: 1\n", "109.19: 2\n", "222.1: 2\n", "154.28: 2\n", "93.18: 1\n", "132.21: 1\n", "214.97: 2\n", "649.97: 1\n", "62.13: 3\n", "299.96: 1\n", "124.83: 1\n", "87.21: 2\n", "317.72: 1\n", "491.2: 1\n", "265.43: 1\n", "137.84: 1\n", "231.12: 1\n", "46.23: 1\n", "88.49: 2\n", "216.92: 1\n", "83.63: 2\n", "275.69: 1\n", "140.27: 2\n", "273.3: 1\n", "254.77: 2\n", "98.0: 2\n", "19.81: 1\n", "790.68: 1\n", "60.52: 1\n", "133.8: 2\n", "439.62: 1\n", "184.9: 2\n", "16.96: 2\n", "111.75: 1\n", "403.54: 1\n", "108.23: 1\n", "286.03: 1\n", "463.02: 1\n", "436.37: 1\n", "141.42: 1\n", "116.26: 1\n", "116.72: 2\n", "292.77: 1\n", "229.74: 2\n", "294.69: 2\n", "99.19: 1\n", "197.97: 2\n", "105.19: 1\n", "94.1: 1\n", "150.48: 1\n", "331.52: 1\n", "267.57: 1\n", "84.72: 1\n", "142.08: 2\n", "274.66: 1\n", "302.47: 1\n", "51.45: 1\n", "586.4: 1\n", "556.34: 1\n", "160.27: 1\n", "363.52: 1\n", "220.68: 1\n", "174.05: 3\n", "34.11: 1\n", "166.87: 1\n", "123.7: 2\n", "298.05: 1\n", "47.2: 1\n", "123.07: 1\n", "91.72: 2\n", "282.44: 1\n", "39.1: 1\n", "287.76: 2\n", "57.62: 1\n", "128.62: 1\n", "76.83: 1\n", "277.73: 1\n", "438.1: 1\n", "223.43: 1\n", "271.44: 1\n", "74.53: 1\n", "77.21: 2\n", "312.8: 1\n", "105.11: 1\n", "223.19: 2\n", "196.52: 2\n", "81.76: 1\n", "447.98: 1\n", "392.0: 1\n", "24.94: 1\n", "429.67: 1\n", "88.22: 1\n", "223.08: 1\n", "164.39: 2\n", "828.98: 1\n", "93.28: 2\n", "54.85: 2\n", "274.23: 1\n", "89.02: 2\n", "446.35: 1\n", "167.17: 1\n", "363.95: 1\n", "59.75: 2\n", "503.74: 1\n", "146.39: 2\n", "103.64: 1\n", "85.17: 2\n", "217.17: 1\n", "62.14: 1\n", "307.64: 1\n", "392.12: 1\n", "105.78: 2\n", "192.9: 1\n", "48.29: 1\n", "240.23: 1\n", "36.51: 1\n", "64.56: 1\n", "195.15: 1\n", "279.08: 1\n", "65.8: 1\n", "125.95: 1\n", "403.73: 1\n", "78.6: 1\n", "196.05: 1\n", "83.82: 1\n", "184.47: 1\n", "24.06: 1\n", "178.67: 1\n", "371.04: 1\n", "52.97: 2\n", "14.05: 1\n", "280.09: 2\n", "45.56: 3\n", "209.21: 1\n", "79.71: 2\n", "179.42: 2\n", "290.27: 1\n", "96.41: 2\n", "156.23: 1\n", "26.89: 2\n", "36.18: 2\n", "65.23: 3\n", "156.0: 1\n", "22.38: 1\n", "241.99: 1\n", "220.13: 1\n", "32.6: 1\n", "66.55: 1\n", "129.5: 2\n", "289.23: 1\n", "585.19: 1\n", "128.95: 1\n", "117.89: 2\n", "400.97: 1\n", "298.53: 1\n", "150.88: 4\n", "56.62: 3\n", "286.72: 1\n", "36.0: 1\n", "65.31: 3\n", "345.71: 1\n", "28.82: 4\n", "85.86: 2\n", "173.48: 1\n", "44.52: 1\n", "451.49: 1\n", "85.61: 1\n", "114.61: 1\n", "119.33: 1\n", "366.08: 2\n", "184.34: 1\n", "680.42: 1\n", "67.78: 1\n", "44.57: 2\n", "45.7: 1\n", "34.39: 2\n", "121.2: 1\n", "357.68: 1\n", "57.34: 1\n", "158.83: 4\n", "271.55: 2\n", "17.18: 1\n", "43.22: 2\n", "169.02: 1\n", "36.43: 1\n", "91.83: 3\n", "132.85: 1\n", "24.01: 2\n", "61.96: 1\n", "64.33: 2\n", "134.64: 2\n", "156.43: 2\n", "73.95: 2\n", "28.55: 1\n", "59.68: 1\n", "15.5: 1\n", "709.36: 1\n", "156.7: 1\n", "277.1: 1\n", "43.76: 2\n", "69.9: 5\n", "76.07: 2\n", "13.72: 1\n", "71.38: 3\n", "20.71: 1\n", "93.98: 1\n", "31.51: 1\n", "185.29: 1\n", "164.93: 1\n", "49.64: 2\n", "202.13: 1\n", "238.25: 1\n", "393.03: 2\n", "527.71: 1\n", "120.1: 2\n", "73.83: 2\n", "47.6: 1\n", "72.27: 2\n", "228.35: 1\n", "184.01: 1\n", "272.76: 1\n", "115.08: 3\n", "182.77: 1\n", "535.89: 1\n", "132.41: 1\n", "184.73: 2\n", "85.65: 1\n", "278.38: 1\n", "211.38: 1\n", "62.58: 3\n", "92.27: 2\n", "84.53: 2\n", "194.05: 1\n", "208.76: 3\n", "96.11: 3\n", "194.1: 1\n", "68.85: 2\n", "162.04: 1\n", "93.68: 2\n", "53.94: 3\n", "95.33: 1\n", "34.24: 1\n", "189.55: 1\n", "609.63: 1\n", "33.32: 2\n", "133.48: 2\n", "36.89: 2\n", "208.79: 1\n", "279.02: 1\n", "156.57: 1\n", "46.89: 1\n", "102.35: 2\n", "113.85: 2\n", "24.58: 1\n", "38.73: 1\n", "26.95: 1\n", "91.5: 2\n", "244.63: 1\n", "263.18: 1\n", "197.73: 1\n", "185.61: 1\n", "71.14: 1\n", "201.34: 2\n", "164.87: 1\n", "54.37: 1\n", "79.66: 4\n", "315.49: 1\n", "39.93: 1\n", "28.95: 1\n", "78.01: 2\n", "289.55: 1\n", "323.4: 1\n", "146.02: 1\n", "307.04: 1\n", "80.78: 2\n", "33.36: 2\n", "47.28: 1\n", "99.6: 1\n", "51.75: 2\n", "246.17: 1\n", "371.48: 1\n", "41.21: 2\n", "394.69: 1\n", "46.93: 1\n", "419.69: 1\n", "45.31: 1\n", "74.55: 3\n", "26.63: 1\n", "87.58: 2\n", "35.6: 1\n", "391.49: 1\n", "152.42: 2\n", "100.08: 1\n", "77.13: 3\n", "68.98: 1\n", "516.24: 1\n", "315.33: 2\n", "646.56: 1\n", "68.78: 2\n", "29.47: 2\n", "221.89: 1\n", "66.48: 1\n", "518.99: 1\n", "199.05: 1\n", "140.43: 2\n", "382.79: 1\n", "285.79: 1\n", "77.83: 1\n", "89.22: 2\n", "145.93: 1\n", "129.82: 1\n", "62.93: 2\n", "250.42: 1\n", "484.32: 1\n", "169.68: 2\n", "34.94: 1\n", "174.64: 3\n", "91.84: 1\n", "88.17: 2\n", "30.35: 2\n", "443.3: 1\n", "280.72: 1\n", "44.3: 2\n", "265.47: 1\n", "168.09: 1\n", "129.21: 2\n", "44.49: 1\n", "122.56: 1\n", "42.92: 1\n", "226.17: 1\n", "556.07: 1\n", "340.24: 1\n", "217.71: 1\n", "283.53: 1\n", "246.22: 1\n", "73.08: 1\n", "125.85: 1\n", "63.61: 1\n", "68.39: 1\n", "16.07: 1\n", "114.42: 1\n", "130.37: 1\n", "232.61: 1\n", "43.3: 4\n", "74.31: 1\n", "46.71: 1\n", "361.62: 1\n", "8.14: 1\n", "225.95: 2\n", "37.48: 1\n", "198.28: 1\n", "112.56: 2\n", "412.42: 1\n", "52.65: 1\n", "51.94: 2\n", "64.75: 1\n", "28.03: 1\n", "451.54: 1\n", "31.19: 1\n", "190.53: 4\n", "146.49: 1\n", "25.01: 2\n", "48.93: 2\n", "454.95: 1\n", "95.12: 1\n", "310.73: 1\n", "46.78: 1\n", "15.3: 1\n", "266.3: 1\n", "67.42: 2\n", "61.54: 1\n", "170.61: 1\n", "15.29: 1\n", "219.11: 1\n", "109.36: 1\n", "15.16: 1\n", "351.73: 1\n", "321.4: 1\n", "78.81: 1\n", "228.7: 1\n", "186.88: 1\n", "119.01: 2\n", "46.49: 2\n", "398.11: 1\n", "338.09: 1\n", "64.98: 2\n", "178.16: 1\n", "275.07: 2\n", "56.66: 2\n", "301.49: 1\n", "198.55: 2\n", "23.48: 2\n", "154.44: 3\n", "315.45: 1\n", "40.23: 2\n", "190.55: 2\n", "186.25: 1\n", "54.13: 1\n", "20.02: 1\n", "461.13: 1\n", "158.93: 1\n", "113.21: 1\n", "203.35: 2\n", "24.91: 1\n", "37.16: 2\n", "57.1: 1\n", "233.52: 1\n", "36.75: 2\n", "53.22: 1\n", "133.83: 1\n", "95.39: 1\n", "37.61: 1\n", "128.99: 3\n", "66.83: 1\n", "311.82: 1\n", "41.76: 3\n", "117.33: 1\n", "55.02: 1\n", "150.57: 1\n", "179.77: 1\n", "380.7: 1\n", "32.14: 1\n", "39.87: 2\n", "204.52: 1\n", "87.02: 1\n", "62.46: 2\n", "186.47: 1\n", "112.82: 1\n", "66.21: 2\n", "156.73: 1\n", "41.54: 1\n", "243.43: 1\n", "157.71: 1\n", "146.18: 1\n", "251.91: 1\n", "441.83: 1\n", "136.23: 1\n", "42.99: 2\n", "118.3: 1\n", "49.42: 2\n", "154.21: 1\n", "60.3: 2\n", "604.02: 1\n", "316.11: 1\n", "40.24: 3\n", "165.65: 1\n", "377.84: 1\n", "232.92: 1\n", "6.86: 1\n", "306.28: 1\n", "274.39: 1\n", "48.84: 1\n", "26.97: 1\n", "675.39: 1\n", "288.63: 1\n", "41.52: 2\n", "55.49: 1\n", "120.86: 1\n", "132.9: 1\n", "259.89: 2\n", "255.14: 1\n", "360.56: 1\n", "207.05: 1\n", "121.64: 2\n", "138.93: 2\n", "154.39: 1\n", "297.44: 1\n", "154.31: 1\n", "469.84: 1\n", "205.35: 1\n", "107.83: 1\n", "53.33: 2\n", "142.6: 1\n", "109.02: 1\n", "122.45: 2\n", "238.02: 1\n", "296.07: 1\n", "76.92: 1\n", "218.48: 1\n", "83.03: 2\n", "212.11: 1\n", "317.59: 1\n", "200.35: 1\n", "76.26: 1\n", "116.74: 2\n", "62.6: 3\n", "466.96: 1\n", "178.87: 1\n", "312.26: 1\n", "81.42: 3\n", "143.7: 1\n", "25.66: 1\n", "179.7: 1\n", "196.34: 1\n", "67.07: 2\n", "180.45: 1\n", "225.32: 1\n", "108.56: 1\n", "165.89: 2\n", "64.94: 1\n", "164.99: 2\n", "226.79: 1\n", "316.4: 1\n", "160.95: 1\n", "177.34: 1\n", "114.49: 1\n", "338.64: 1\n", "61.13: 1\n", "81.51: 1\n", "399.84: 1\n", "39.53: 1\n", "224.49: 1\n", "41.01: 2\n", "76.53: 2\n", "119.96: 3\n", "231.41: 1\n", "192.82: 1\n", "142.72: 1\n", "127.92: 2\n", "148.49: 1\n", "385.64: 1\n", "208.1: 1\n", "36.01: 1\n", "37.31: 1\n", "92.06: 1\n", "129.68: 1\n", "167.6: 1\n", "349.88: 1\n", "31.01: 3\n", "63.86: 1\n", "117.36: 2\n", "252.56: 1\n", "40.52: 1\n", "144.26: 1\n", "29.2: 1\n", "79.95: 1\n", "61.21: 1\n", "361.8: 1\n", "164.44: 2\n", "147.36: 2\n", "446.55: 1\n", "320.56: 1\n", "154.95: 1\n", "730.92: 1\n", "37.13: 1\n", "147.74: 1\n", "371.85: 1\n", "216.49: 1\n", "107.49: 1\n", "388.78: 1\n", "172.28: 1\n", "102.68: 1\n", "346.21: 1\n", "461.83: 1\n", "554.31: 1\n", "211.91: 1\n", "63.5: 2\n", "223.71: 2\n", "78.74: 1\n", "119.8: 1\n", "86.33: 1\n", "94.73: 1\n", "47.86: 1\n", "322.2: 1\n", "163.01: 1\n", "263.49: 1\n", "288.36: 1\n", "396.33: 2\n", "278.98: 1\n", "104.91: 1\n", "201.78: 1\n", "118.19: 1\n", "110.91: 2\n", "517.2: 1\n", "322.0: 1\n", "579.26: 1\n", "292.55: 1\n", "110.36: 1\n", "79.85: 2\n", "254.55: 2\n", "196.47: 2\n", "354.97: 1\n", "266.35: 1\n", "59.2: 1\n", "216.77: 1\n", "175.89: 1\n", "125.1: 1\n", "198.39: 1\n", "27.09: 1\n", "472.84: 1\n", "433.93: 1\n", "349.65: 1\n", "16.6: 1\n", "203.88: 1\n", "110.45: 1\n", "78.84: 1\n", "45.92: 2\n", "71.84: 2\n", "91.89: 1\n", "137.38: 1\n", "73.55: 1\n", "498.56: 1\n", "83.0: 2\n", "90.02: 2\n", "14.92: 1\n", "120.79: 3\n", "17.99: 1\n", "69.35: 2\n", "35.38: 1\n", "141.58: 1\n", "52.15: 2\n", "151.64: 1\n", "262.62: 1\n", "222.61: 1\n", "248.96: 1\n", "360.74: 1\n", "211.31: 1\n", "60.2: 1\n", "194.01: 1\n", "82.42: 1\n", "54.09: 2\n", "7.52: 1\n", "164.82: 2\n", "101.11: 2\n", "111.2: 1\n", "229.52: 1\n", "95.5: 1\n", "55.85: 2\n", "137.92: 2\n", "69.03: 2\n", "59.77: 1\n", "77.58: 1\n", "170.03: 1\n", "278.89: 1\n", "167.22: 1\n", "272.82: 1\n", "154.99: 1\n", "24.61: 1\n", "86.5: 1\n", "435.99: 1\n", "232.39: 1\n", "117.63: 1\n", "166.0: 1\n", "96.28: 1\n", "74.2: 1\n", "169.61: 1\n", "12.24: 1\n", "29.07: 1\n", "594.41: 1\n", "14.44: 1\n", "89.36: 2\n", "126.41: 1\n", "92.32: 1\n", "184.85: 2\n", "119.79: 1\n", "29.3: 1\n", "463.42: 1\n", "154.48: 2\n", "90.81: 1\n", "242.9: 1\n", "156.5: 1\n", "192.73: 1\n", "66.39: 1\n", "98.23: 1\n", "148.81: 1\n", "208.46: 1\n", "331.28: 1\n", "25.11: 1\n", "185.96: 1\n", "102.1: 3\n", "306.94: 1\n", "345.64: 2\n", "186.98: 1\n", "100.12: 1\n", "121.07: 1\n", "141.01: 1\n", "164.14: 1\n", "38.48: 2\n", "141.03: 2\n", "142.92: 1\n", "369.38: 1\n", "26.33: 2\n", "12.28: 1\n", "98.27: 1\n", "318.33: 1\n", "98.29: 1\n", "183.52: 1\n", "65.63: 1\n", "46.97: 1\n", "113.89: 2\n", "92.46: 1\n", "29.68: 2\n", "77.45: 1\n", "188.33: 1\n", "47.24: 3\n", "129.9: 3\n", "17.65: 1\n", "21.92: 1\n", "76.96: 1\n", "188.48: 1\n", "34.82: 2\n", "35.18: 1\n", "54.01: 1\n", "48.25: 1\n", "37.74: 1\n", "74.46: 2\n", "138.99: 2\n", "109.04: 1\n", "171.65: 1\n", "297.97: 1\n", "87.82: 1\n", "403.97: 1\n", "223.54: 1\n", "267.14: 1\n", "206.58: 1\n", "346.75: 1\n", "215.34: 1\n", "49.74: 2\n", "80.89: 1\n", "109.4: 1\n", "136.36: 2\n", "219.41: 2\n", "699.32: 1\n", "180.64: 1\n", "354.08: 1\n", "235.29: 1\n", "45.24: 3\n", "102.76: 2\n", "89.45: 1\n", "33.71: 2\n", "298.97: 1\n", "23.12: 2\n", "642.95: 1\n", "64.01: 2\n", "769.03: 1\n", "256.07: 1\n", "143.66: 1\n", "54.49: 1\n", "95.89: 1\n", "94.34: 2\n", "39.11: 1\n", "40.45: 1\n", "282.23: 1\n", "258.05: 1\n", "418.6: 2\n", "44.12: 2\n", "91.22: 2\n", "138.37: 1\n", "49.02: 2\n", "33.51: 1\n", "62.26: 2\n", "378.25: 1\n", "558.3: 1\n", "292.66: 3\n", "215.9: 1\n", "79.38: 1\n", "291.21: 1\n", "136.67: 1\n", "477.68: 1\n", "366.68: 1\n", "99.59: 2\n", "25.15: 1\n", "113.78: 1\n", "222.48: 1\n", "95.01: 1\n", "134.82: 1\n", "315.95: 1\n", "81.47: 2\n", "139.62: 1\n", "186.86: 1\n", "115.11: 2\n", "168.93: 2\n", "95.38: 1\n", "17.16: 1\n", "326.95: 1\n", "20.07: 1\n", "87.7: 1\n", "92.66: 3\n", "281.43: 1\n", "113.64: 2\n", "537.99: 1\n", "95.6: 2\n", "276.77: 1\n", "194.28: 1\n", "162.77: 1\n", "83.97: 1\n", "197.12: 1\n", "44.08: 1\n", "94.21: 1\n", "137.98: 1\n", "416.07: 1\n", "264.23: 1\n", "67.41: 1\n", "12.29: 2\n", "168.53: 2\n", "65.86: 1\n", "183.26: 1\n", "81.13: 2\n", "186.61: 1\n", "59.44: 2\n", "59.84: 1\n", "97.03: 1\n", "324.36: 1\n", "44.31: 1\n", "157.14: 1\n", "114.28: 1\n", "139.65: 2\n", "104.78: 1\n", "617.43: 1\n", "51.96: 1\n", "268.31: 1\n", "101.94: 2\n", "338.32: 1\n", "161.19: 2\n", "32.76: 1\n", "30.24: 2\n", "238.48: 1\n", "142.45: 2\n", "264.61: 1\n", "30.2: 1\n", "262.42: 1\n", "102.08: 2\n", "31.95: 1\n", "63.55: 2\n", "170.45: 2\n", "169.35: 1\n", "95.17: 2\n", "143.87: 1\n", "210.06: 1\n", "92.51: 1\n", "215.53: 1\n", "367.1: 2\n", "96.72: 1\n", "5.56: 1\n", "8.77: 1\n", "236.32: 1\n", "342.46: 1\n", "155.34: 1\n", "85.59: 1\n", "249.81: 1\n", "142.77: 1\n", "16.32: 2\n", "250.56: 2\n", "36.82: 1\n", "6.35: 1\n", "30.27: 1\n", "32.22: 1\n", "36.35: 3\n", "270.26: 2\n", "44.59: 2\n", "14.71: 2\n", "21.8: 1\n", "39.59: 1\n", "79.27: 1\n", "63.72: 2\n", "249.03: 1\n", "41.09: 1\n", "81.77: 1\n", "56.08: 2\n", "225.62: 1\n", "46.13: 2\n", "120.13: 1\n", "148.1: 1\n", "186.94: 1\n", "74.02: 1\n", "49.16: 3\n", "399.95: 1\n", "45.43: 1\n", "377.77: 1\n", "107.19: 1\n", "85.94: 1\n", "104.14: 2\n", "113.3: 1\n", "195.97: 1\n", "27.68: 1\n", "76.85: 1\n", "132.93: 2\n", "701.25: 1\n", "460.67: 1\n", "29.92: 1\n", "66.92: 2\n", "50.26: 1\n", "269.84: 1\n", "58.0: 4\n", "336.42: 1\n", "155.68: 1\n", "128.45: 1\n", "226.49: 1\n", "369.51: 1\n", "7.88: 1\n", "60.13: 1\n", "144.28: 1\n", "107.08: 1\n", "51.46: 1\n", "57.79: 2\n", "243.27: 1\n", "104.61: 1\n", "33.38: 1\n", "92.87: 1\n", "37.08: 2\n", "187.99: 1\n", "177.69: 1\n", "99.81: 2\n", "206.03: 1\n", "168.25: 1\n", "63.3: 1\n", "312.6: 1\n", "393.93: 1\n", "206.05: 2\n", "30.96: 1\n", "25.54: 1\n", "126.4: 1\n", "236.43: 1\n", "57.22: 1\n", "162.12: 1\n", "166.01: 1\n", "384.81: 1\n", "39.72: 1\n", "87.06: 1\n", "104.36: 1\n", "146.67: 1\n", "257.23: 1\n", "110.97: 2\n", "267.25: 1\n", "82.65: 1\n", "205.51: 1\n", "48.7: 3\n", "95.54: 2\n", "115.41: 1\n", "168.21: 1\n", "48.15: 3\n", "63.98: 2\n", "75.57: 2\n", "472.57: 1\n", "144.27: 2\n", "187.08: 2\n", "114.99: 1\n", "423.22: 1\n", "114.68: 1\n", "263.09: 1\n", "14.57: 2\n", "54.21: 1\n", "92.64: 1\n", "99.01: 1\n", "97.95: 1\n", "97.01: 1\n", "607.24: 1\n", "116.81: 1\n", "254.93: 1\n", "192.5: 1\n", "28.54: 1\n", "244.73: 1\n", "96.92: 1\n", "328.86: 1\n", "208.24: 1\n", "227.58: 1\n", "161.78: 3\n", "37.78: 1\n", "61.92: 3\n", "76.45: 1\n", "82.48: 2\n", "28.64: 1\n", "114.69: 1\n", "97.35: 1\n", "50.45: 3\n", "63.91: 1\n", "125.91: 1\n", "278.58: 1\n", "159.99: 1\n", "132.77: 1\n", "32.47: 1\n", "219.61: 1\n", "140.81: 1\n", "128.66: 1\n", "19.22: 1\n", "726.27: 1\n", "296.69: 2\n", "97.58: 1\n", "129.47: 1\n", "146.28: 1\n", "117.81: 2\n", "64.2: 1\n", "140.58: 2\n", "73.36: 1\n", "114.52: 1\n", "143.04: 2\n", "110.18: 1\n", "232.52: 1\n", "232.47: 2\n", "84.38: 1\n", "99.64: 1\n", "46.45: 1\n", "147.47: 2\n", "71.88: 2\n", "32.45: 2\n", "134.08: 1\n", "20.9: 2\n", "307.97: 1\n", "123.77: 1\n", "78.64: 1\n", "46.08: 3\n", "89.94: 2\n", "69.94: 2\n", "246.51: 2\n", "336.49: 1\n", "55.98: 2\n", "39.81: 1\n", "79.3: 1\n", "197.32: 1\n", "86.28: 2\n", "62.31: 1\n", "152.48: 2\n", "67.98: 2\n", "62.3: 4\n", "57.91: 1\n", "103.96: 1\n", "585.92: 1\n", "52.26: 2\n", "76.73: 1\n", "314.26: 2\n", "169.21: 1\n", "165.11: 1\n", "426.32: 1\n", "223.57: 1\n", "134.55: 1\n", "58.41: 2\n", "35.7: 1\n", "39.03: 1\n", "27.15: 2\n", "49.93: 1\n", "108.71: 1\n", "56.41: 1\n", "38.2: 1\n", "102.32: 2\n", "92.34: 1\n", "40.88: 2\n", "89.42: 3\n", "89.68: 1\n", "199.17: 3\n", "266.75: 1\n", "294.73: 1\n", "48.36: 2\n", "202.85: 1\n", "43.42: 3\n", "222.49: 1\n", "640.25: 1\n", "111.74: 1\n", "167.21: 1\n", "419.24: 1\n", "373.59: 1\n", "339.82: 1\n", "175.4: 1\n", "67.29: 2\n", "32.93: 1\n", "81.59: 2\n", "33.53: 1\n", "25.89: 1\n", "29.18: 2\n", "214.54: 1\n", "123.96: 1\n", "60.14: 2\n", "92.88: 2\n", "65.97: 2\n", "449.26: 1\n", "129.03: 1\n", "81.07: 1\n", "73.0: 1\n", "153.47: 1\n", "227.61: 1\n", "273.31: 1\n", "196.04: 3\n", "290.5: 1\n", "155.95: 1\n", "581.12: 1\n", "31.16: 1\n", "79.98: 1\n", "408.07: 1\n", "153.84: 1\n", "45.61: 2\n", "142.37: 1\n", "40.35: 1\n", "107.81: 1\n", "147.01: 1\n", "22.37: 1\n", "235.68: 1\n", "89.79: 2\n", "525.3: 1\n", "230.96: 1\n", "96.52: 1\n", "116.54: 1\n", "167.62: 1\n", "580.1: 1\n", "110.11: 1\n", "284.43: 1\n", "146.64: 1\n", "82.07: 2\n", "424.52: 1\n", "203.17: 1\n", "168.22: 1\n", "84.92: 1\n", "88.44: 1\n", "149.62: 1\n", "39.08: 2\n", "37.19: 2\n", "119.6: 1\n", "21.64: 1\n", "567.15: 1\n", "330.6: 1\n", "144.66: 2\n", "139.57: 1\n", "348.4: 1\n", "103.83: 2\n", "95.4: 2\n", "24.77: 1\n", "279.59: 1\n", "10.03: 2\n", "101.02: 2\n", "190.95: 1\n", "81.23: 1\n", "213.66: 1\n", "533.28: 1\n", "40.08: 3\n", "263.24: 1\n", "53.1: 1\n", "43.65: 1\n", "38.28: 1\n", "301.79: 1\n", "365.82: 1\n", "47.41: 2\n", "92.29: 1\n", "171.42: 2\n", "87.86: 2\n", "51.25: 1\n", "190.03: 1\n", "23.91: 2\n", "311.03: 2\n", "36.99: 2\n", "301.81: 1\n", "173.66: 1\n", "333.43: 1\n", "270.7: 1\n", "242.41: 1\n", "295.47: 1\n", "147.96: 1\n", "104.22: 2\n", "54.52: 2\n", "138.4: 1\n", "183.38: 1\n", "382.01: 1\n", "100.31: 1\n", "244.71: 1\n", "152.49: 1\n", "62.48: 1\n", "33.63: 1\n", "129.17: 1\n", "74.08: 1\n", "85.3: 1\n", "189.28: 2\n", "162.19: 2\n", "249.96: 1\n", "73.98: 2\n", "194.9: 1\n", "33.2: 1\n", "86.8: 3\n", "29.08: 1\n", "270.97: 1\n", "299.04: 1\n", "87.17: 2\n", "228.29: 1\n", "37.23: 1\n", "143.97: 1\n", "172.64: 2\n", "171.48: 3\n", "461.73: 2\n", "238.17: 1\n", "77.52: 1\n", "38.52: 1\n", "234.73: 1\n", "219.35: 2\n", "190.29: 1\n", "39.95: 3\n", "132.07: 1\n", "110.87: 2\n", "27.72: 2\n", "343.32: 1\n", "297.26: 1\n", "151.2: 1\n", "236.21: 1\n", "198.08: 2\n", "124.69: 1\n", "561.06: 1\n", "632.2: 1\n", "179.32: 2\n", "305.82: 2\n", "418.03: 1\n", "291.47: 1\n", "257.41: 1\n", "129.51: 1\n", "263.1: 1\n", "86.42: 1\n", "742.9: 1\n", "508.88: 1\n", "83.09: 1\n", "265.75: 1\n", "226.37: 1\n", "428.6: 1\n", "145.37: 1\n", "55.95: 1\n", "46.51: 2\n", "133.31: 2\n", "117.92: 1\n", "18.82: 1\n", "53.95: 1\n", "142.82: 1\n", "360.45: 1\n", "52.7: 1\n", "88.88: 2\n", "33.98: 1\n", "21.45: 2\n", "33.97: 3\n", "46.62: 2\n", "97.67: 2\n", "34.25: 2\n", "266.78: 1\n", "327.99: 1\n", "115.35: 3\n", "26.01: 1\n", "175.24: 1\n", "108.05: 1\n", "153.28: 1\n", "274.04: 1\n", "98.96: 1\n", "93.41: 1\n", "19.48: 1\n", "210.47: 2\n", "55.71: 1\n", "53.45: 1\n", "37.09: 1\n", "178.28: 1\n", "346.14: 1\n", "67.0: 1\n", "155.32: 1\n", "147.14: 3\n", "124.76: 1\n", "450.95: 1\n", "448.4: 1\n", "290.72: 1\n", "108.41: 1\n", "282.84: 1\n", "51.42: 1\n", "219.24: 1\n", "41.82: 2\n", "270.72: 1\n", "116.95: 1\n", "316.93: 1\n", "30.74: 1\n", "52.05: 1\n", "221.98: 1\n", "127.21: 2\n", "114.13: 1\n", "175.94: 1\n", "199.78: 1\n", "189.49: 1\n", "226.63: 1\n", "128.61: 1\n", "59.19: 1\n", "61.89: 3\n", "55.21: 1\n", "149.66: 4\n", "105.13: 1\n", "53.35: 1\n", "176.81: 1\n", "236.67: 1\n", "62.05: 1\n", "359.64: 1\n", "101.8: 2\n", "26.62: 1\n", "232.8: 1\n", "58.37: 1\n", "71.86: 2\n", "79.31: 1\n", "36.33: 1\n", "37.3: 2\n", "101.59: 1\n", "24.08: 1\n", "28.56: 2\n", "71.03: 1\n", "269.25: 1\n", "94.59: 5\n", "506.56: 1\n", "27.02: 2\n", "90.63: 1\n", "17.12: 2\n", "279.58: 1\n", "134.69: 1\n", "346.93: 1\n", "202.63: 1\n", "93.63: 1\n", "78.83: 1\n", "144.51: 1\n", "167.75: 1\n", "59.8: 1\n", "78.11: 1\n", "369.73: 1\n", "221.49: 1\n", "273.49: 1\n", "59.07: 2\n", "292.99: 1\n", "123.38: 1\n", "52.44: 1\n", "24.62: 1\n", "28.89: 1\n", "25.49: 1\n", "43.08: 1\n", "337.79: 1\n", "122.92: 1\n", "22.49: 2\n", "272.28: 1\n", "595.8: 1\n", "47.59: 1\n", "257.13: 1\n", "34.35: 1\n", "64.19: 1\n", "113.2: 1\n", "135.92: 1\n", "22.16: 1\n", "258.65: 1\n", "151.08: 1\n", "183.4: 1\n", "359.5: 1\n", "163.49: 1\n", "81.98: 2\n", "94.52: 1\n", "14.84: 1\n", "199.47: 1\n", "39.99: 1\n", "61.4: 1\n", "32.24: 1\n", "139.99: 1\n", "27.49: 2\n", "78.1: 2\n", "58.6: 1\n", "57.78: 1\n", "14.16: 1\n", "238.84: 1\n", "174.61: 1\n", "52.36: 1\n", "84.8: 1\n", "149.71: 4\n", "90.62: 1\n", "54.3: 1\n", "31.94: 1\n", "196.74: 1\n", "46.56: 2\n", "166.38: 1\n", "195.95: 1\n", "195.02: 1\n", "160.56: 2\n", "120.44: 1\n", "62.91: 1\n", "257.47: 1\n", "218.56: 1\n", "35.83: 1\n", "74.78: 1\n", "216.54: 1\n", "297.41: 1\n", "62.74: 1\n", "228.86: 1\n", "62.08: 2\n", "50.25: 1\n", "111.14: 1\n", "35.48: 1\n", "357.22: 1\n", "36.93: 2\n", "243.65: 1\n", "188.26: 1\n", "110.99: 1\n", "175.82: 1\n", "15.66: 3\n", "37.58: 2\n", "58.49: 1\n", "65.73: 1\n", "79.2: 2\n", "91.51: 1\n", "8.27: 2\n", "49.22: 1\n", "138.77: 2\n", "89.46: 1\n", "235.97: 2\n", "62.25: 2\n", "215.35: 1\n", "35.9: 1\n", "229.94: 1\n", "475.06: 1\n", "405.61: 1\n", "42.64: 3\n", "48.21: 1\n", "651.55: 1\n", "154.3: 2\n", "131.6: 3\n", "213.78: 1\n", "457.79: 1\n", "415.48: 1\n", "175.32: 1\n", "109.93: 1\n", "540.7: 1\n", "128.71: 1\n", "144.13: 3\n", "193.43: 1\n", "51.91: 1\n", "192.6: 3\n", "38.26: 2\n", "196.07: 1\n", "254.02: 1\n", "156.72: 1\n", "136.42: 2\n", "51.66: 2\n", "287.54: 1\n", "121.44: 2\n", "75.04: 1\n", "301.12: 1\n", "158.51: 3\n", "32.99: 2\n", "54.55: 4\n", "71.77: 1\n", "212.07: 2\n", "546.99: 1\n", "45.74: 3\n", "105.52: 2\n", "152.54: 1\n", "12.58: 1\n", "139.61: 2\n", "327.11: 1\n", "240.53: 1\n", "208.41: 1\n", "47.94: 2\n", "173.4: 1\n", "65.47: 1\n", "70.89: 3\n", "86.36: 1\n", "49.83: 1\n", "249.28: 1\n", "21.16: 1\n", "303.72: 1\n", "26.72: 1\n", "169.36: 1\n", "174.63: 2\n", "111.88: 2\n", "544.32: 1\n", "130.46: 2\n", "93.61: 2\n", "104.87: 2\n", "136.94: 1\n", "49.81: 2\n", "40.05: 3\n", "71.62: 2\n", "62.28: 1\n", "501.46: 1\n", "185.73: 1\n", "228.67: 2\n", "115.37: 1\n", "255.3: 1\n", "54.27: 1\n", "58.42: 1\n", "61.44: 1\n", "59.69: 3\n", "111.43: 2\n", "27.38: 1\n", "104.72: 1\n", "223.48: 1\n", "87.18: 1\n", "377.58: 1\n", "45.9: 1\n", "125.31: 1\n", "56.52: 1\n", "117.62: 1\n", "51.59: 4\n", "100.9: 2\n", "334.14: 1\n", "323.25: 1\n", "46.17: 2\n", "145.02: 1\n", "64.78: 2\n", "87.2: 1\n", "48.27: 4\n", "59.51: 2\n", "58.24: 1\n", "37.2: 2\n", "10.63: 1\n", "90.09: 1\n", "91.86: 1\n", "20.56: 1\n", "213.25: 1\n", "101.21: 1\n", "32.75: 1\n", "61.37: 1\n", "73.68: 1\n", "12.75: 1\n", "278.55: 1\n", "10.45: 1\n", "164.31: 1\n", "61.02: 3\n", "542.68: 1\n", "156.37: 4\n", "299.67: 1\n", "125.24: 1\n", "29.72: 3\n", "124.78: 2\n", "235.6: 1\n", "83.57: 1\n", "171.91: 1\n", "112.46: 1\n", "83.61: 1\n", "124.97: 1\n", "226.53: 1\n", "183.11: 1\n", "169.54: 1\n", "124.45: 2\n", "24.04: 2\n", "123.76: 2\n", "101.0: 2\n", "248.51: 2\n", "120.77: 1\n", "68.99: 1\n", "117.59: 1\n", "74.61: 1\n", "248.52: 1\n", "393.67: 1\n", "317.3: 1\n", "134.71: 1\n", "46.06: 1\n", "470.84: 1\n", "105.45: 1\n", "187.25: 1\n", "124.13: 1\n", "11.6: 1\n", "199.0: 2\n", "54.65: 1\n", "130.06: 1\n", "125.84: 1\n", "179.27: 1\n", "32.65: 1\n", "164.22: 1\n", "268.14: 1\n", "452.4: 1\n", "204.53: 1\n", "135.78: 2\n", "187.15: 1\n", "564.44: 1\n", "434.85: 1\n", "95.18: 1\n", "96.03: 1\n", "141.9: 1\n", "187.76: 1\n", "45.14: 1\n", "211.9: 1\n", "156.35: 1\n", "108.74: 2\n", "89.81: 1\n", "24.49: 2\n", "104.43: 1\n", "47.5: 1\n", "22.97: 2\n", "381.96: 2\n", "36.73: 3\n", "104.42: 1\n", "96.21: 3\n", "62.67: 1\n", "55.83: 2\n", "284.98: 1\n", "165.67: 1\n", "149.46: 1\n", "265.67: 2\n", "290.92: 2\n", "95.04: 1\n", "72.63: 2\n", "118.1: 1\n", "102.01: 1\n", "89.54: 1\n", "303.18: 1\n", "195.77: 1\n", "174.0: 1\n", "324.71: 1\n", "45.69: 3\n", "52.23: 1\n", "23.44: 1\n", "5.57: 1\n", "347.91: 1\n", "505.13: 1\n", "57.61: 1\n", "53.88: 2\n", "99.82: 2\n", "67.61: 1\n", "71.0: 1\n", "37.18: 1\n", "66.74: 2\n", "84.6: 1\n", "53.72: 1\n", "311.21: 2\n", "130.6: 1\n", "165.43: 1\n", "28.96: 1\n", "74.98: 2\n", "29.57: 1\n", "155.25: 1\n", "52.2: 1\n", "21.61: 1\n", "120.21: 1\n", "208.93: 1\n", "351.04: 1\n", "56.37: 1\n", "194.39: 1\n", "203.85: 1\n", "95.15: 2\n", "127.26: 1\n", "57.46: 1\n", "170.76: 1\n", "153.05: 2\n", "296.38: 1\n", "78.13: 1\n", "176.4: 1\n", "233.4: 1\n", "62.04: 1\n", "85.18: 1\n", "92.15: 1\n", "86.02: 1\n", "28.19: 2\n", "67.19: 1\n", "203.19: 1\n", "78.08: 1\n", "113.63: 1\n", "121.0: 1\n", "30.55: 1\n", "384.36: 1\n", "96.54: 2\n", "25.65: 2\n", "64.5: 1\n", "96.49: 1\n", "180.82: 2\n", "117.28: 1\n", "226.62: 1\n", "168.0: 1\n", "86.78: 3\n", "147.02: 1\n", "220.03: 1\n", "54.76: 1\n", "232.24: 1\n", "151.96: 1\n", "35.84: 1\n", "145.71: 1\n", "288.89: 1\n", "94.5: 1\n", "31.8: 2\n", "135.99: 1\n", "28.23: 1\n", "147.89: 1\n", "34.0: 1\n", "70.43: 1\n", "41.88: 2\n", "154.76: 2\n", "92.18: 1\n", "113.94: 2\n", "85.48: 1\n", "65.11: 3\n", "35.58: 1\n", "103.07: 1\n", "29.96: 2\n", "38.66: 1\n", "11.29: 1\n", "55.72: 3\n", "44.56: 1\n", "98.14: 1\n", "51.83: 1\n", "111.46: 1\n", "27.96: 1\n", "52.21: 1\n", "33.34: 1\n", "96.06: 1\n", "133.04: 3\n", "302.01: 1\n", "275.56: 1\n", "103.24: 1\n", "229.01: 1\n", "53.4: 2\n", "156.02: 1\n", "499.82: 1\n", "30.59: 2\n", "61.64: 1\n", "39.15: 1\n", "15.11: 1\n", "92.72: 1\n", "60.88: 1\n", "40.82: 3\n", "30.6: 1\n", "27.98: 1\n", "126.22: 1\n", "22.04: 1\n", "11.62: 1\n", "38.94: 2\n", "27.57: 1\n", "61.57: 1\n", "40.57: 1\n", "38.72: 1\n", "102.0: 1\n", "58.93: 1\n", "61.66: 1\n", "41.66: 2\n", "62.39: 1\n", "39.38: 2\n", "113.43: 1\n", "56.9: 1\n", "14.38: 1\n", "21.34: 2\n", "68.26: 1\n", "46.9: 2\n", "175.26: 1\n", "32.63: 1\n", "44.54: 1\n", "57.65: 2\n", "110.44: 1\n", "27.4: 2\n", "20.44: 1\n", "33.81: 1\n", "35.98: 2\n", "146.17: 1\n", "9.41: 2\n", "44.97: 1\n", "174.98: 1\n", "53.71: 2\n", "21.11: 1\n", "74.71: 1\n", "61.88: 1\n", "83.5: 3\n", "96.86: 2\n", "56.92: 1\n", "273.53: 1\n", "88.6: 1\n", "165.63: 2\n", "56.05: 2\n", "20.89: 1\n", "70.58: 2\n", "214.16: 1\n", "178.77: 1\n", "8.72: 1\n", "10.59: 1\n", "375.67: 1\n", "159.58: 1\n", "78.82: 3\n", "206.66: 1\n", "92.26: 2\n", "75.43: 2\n", "214.04: 1\n", "342.83: 1\n", "21.3: 1\n", "70.5: 2\n", "38.35: 1\n", "48.26: 1\n", "147.21: 1\n", "28.0: 1\n", "257.36: 1\n", "22.05: 1\n", "323.3: 1\n", "643.28: 1\n", "19.18: 1\n", "100.23: 1\n", "49.49: 1\n", "186.59: 1\n", "38.25: 2\n", "123.01: 1\n", "22.31: 2\n", "114.79: 2\n", "189.12: 1\n", "272.78: 2\n", "54.96: 2\n", "154.22: 2\n", "202.56: 1\n", "26.78: 1\n", "42.54: 3\n", "85.28: 2\n", "71.99: 2\n", "20.0: 1\n", "370.65: 1\n", "85.78: 2\n", "181.2: 1\n", "275.41: 1\n", "306.14: 1\n", "68.18: 2\n", "57.11: 1\n", "204.05: 1\n", "102.5: 1\n", "280.26: 1\n", "447.62: 1\n", "27.51: 1\n", "16.95: 1\n", "67.54: 1\n", "32.64: 1\n", "359.26: 1\n", "240.34: 1\n", "331.56: 1\n", "140.47: 2\n", "216.01: 2\n", "160.44: 1\n", "20.53: 1\n", "207.62: 1\n", "58.08: 1\n", "76.74: 2\n", "432.03: 1\n", "367.36: 1\n", "212.32: 1\n", "115.56: 2\n", "15.35: 1\n", "42.91: 2\n", "338.41: 1\n", "389.73: 1\n", "105.92: 2\n", "45.34: 1\n", "403.68: 1\n", "155.18: 1\n", "57.04: 1\n", "47.17: 1\n", "159.84: 1\n", "99.02: 1\n", "618.2: 1\n", "75.14: 2\n", "217.58: 1\n", "75.36: 4\n", "151.87: 1\n", "184.19: 2\n", "247.15: 1\n", "97.24: 1\n", "41.5: 4\n", "178.68: 2\n", "178.13: 1\n", "149.98: 3\n", "15.58: 1\n", "34.48: 1\n", "170.26: 1\n", "120.81: 2\n", "56.94: 2\n", "85.04: 1\n", "104.38: 1\n", "71.58: 1\n", "266.92: 1\n", "130.11: 1\n", "71.85: 2\n", "166.03: 1\n", "128.33: 1\n", "162.46: 2\n", "59.37: 2\n", "206.01: 2\n", "168.15: 2\n", "233.77: 1\n", "313.03: 1\n", "150.99: 1\n", "125.16: 1\n", "52.77: 1\n", "450.03: 1\n", "26.99: 1\n", "266.99: 1\n", "262.99: 1\n", "86.84: 1\n", "161.06: 2\n", "139.98: 1\n", "90.59: 2\n", "22.42: 1\n", "157.29: 1\n", "105.84: 1\n", "189.96: 1\n", "50.27: 1\n", "30.34: 1\n", "288.05: 1\n", "463.29: 1\n", "65.38: 1\n", "71.17: 1\n", "353.26: 1\n", "110.65: 1\n", "132.33: 5\n", "114.41: 1\n", "119.13: 1\n", "59.63: 3\n", "384.37: 1\n", "119.25: 1\n", "98.15: 1\n", "110.37: 2\n", "72.09: 2\n", "236.71: 1\n", "104.64: 1\n", "73.31: 1\n", "79.29: 2\n", "277.95: 1\n", "56.01: 1\n", "398.07: 1\n", "33.06: 2\n", "52.22: 1\n", "145.07: 2\n", "116.97: 1\n", "509.51: 1\n", "532.79: 1\n", "468.63: 1\n", "240.43: 1\n", "494.66: 1\n", "79.62: 1\n", "218.57: 1\n", "268.27: 1\n", "75.16: 2\n", "148.47: 3\n", "119.55: 1\n", "45.64: 2\n", "52.28: 2\n", "350.85: 1\n", "272.58: 1\n", "29.29: 1\n", "70.18: 2\n", "227.19: 2\n", "66.24: 1\n", "90.26: 2\n", "6.06: 1\n", "176.24: 3\n", "235.36: 1\n", "209.24: 1\n", "249.35: 1\n", "62.79: 2\n", "278.82: 1\n", "528.5: 1\n", "206.62: 1\n", "45.21: 1\n", "93.62: 1\n", "67.28: 1\n", "91.97: 1\n", "179.51: 1\n", "59.54: 2\n", "20.31: 1\n", "175.18: 1\n", "109.53: 3\n", "195.8: 1\n", "307.51: 1\n", "83.47: 1\n", "41.38: 1\n", "196.08: 1\n", "45.41: 1\n", "61.5: 4\n", "37.35: 3\n", "121.58: 2\n", "219.22: 1\n", "112.8: 3\n", "92.22: 1\n", "131.51: 1\n", "394.86: 1\n", "257.52: 2\n", "48.45: 1\n", "182.85: 1\n", "145.94: 1\n", "371.5: 1\n", "111.55: 2\n", "303.64: 1\n", "149.69: 1\n", "209.98: 1\n", "62.07: 1\n", "299.01: 1\n", "34.4: 1\n", "45.15: 1\n", "87.05: 1\n", "120.24: 2\n", "211.39: 1\n", "149.09: 1\n", "35.13: 1\n", "39.98: 1\n", "58.55: 1\n", "73.07: 2\n", "179.67: 1\n", "30.46: 1\n", "159.46: 2\n", "567.37: 1\n", "313.74: 1\n", "92.85: 1\n", "134.09: 1\n", "208.16: 1\n", "258.76: 1\n", "18.64: 1\n", "165.37: 1\n", "61.03: 2\n", "129.63: 1\n", "41.0: 3\n", "42.89: 1\n", "43.46: 1\n", "46.11: 3\n", "94.13: 1\n", "585.99: 2\n", "99.46: 1\n", "136.72: 1\n", "81.73: 1\n", "123.68: 1\n", "375.11: 1\n", "82.32: 1\n", "24.86: 2\n", "74.32: 1\n", "78.55: 1\n", "151.88: 1\n", "142.96: 1\n", "12.76: 2\n", "191.25: 1\n", "38.06: 1\n", "18.88: 2\n", "93.3: 1\n", "100.42: 2\n", "69.74: 1\n", "58.57: 2\n", "84.32: 1\n", "35.19: 1\n", "26.08: 1\n", "50.9: 1\n", "319.74: 1\n", "161.91: 1\n", "284.86: 1\n", "79.86: 2\n", "99.74: 1\n", "182.68: 1\n", "15.31: 1\n", "35.42: 1\n", "116.05: 1\n", "22.39: 2\n", "76.87: 1\n", "187.92: 1\n", "85.77: 2\n", "91.81: 1\n", "356.7: 1\n", "54.81: 1\n", "73.32: 3\n", "50.64: 2\n", "62.61: 3\n", "18.79: 1\n", "46.8: 1\n", "65.28: 3\n", "46.94: 1\n", "297.72: 1\n", "51.0: 1\n", "3.39: 1\n", "139.39: 1\n", "28.21: 1\n", "90.42: 1\n", "32.08: 2\n", "86.14: 1\n", "370.42: 1\n", "54.43: 2\n", "261.17: 1\n", "18.76: 1\n", "194.43: 1\n", "181.52: 1\n", "134.13: 1\n", "246.4: 1\n", "48.81: 1\n", "23.81: 1\n", "460.07: 2\n", "36.57: 1\n", "68.05: 1\n", "5.44: 1\n", "53.46: 1\n", "135.94: 2\n", "53.73: 1\n", "65.75: 1\n", "148.14: 1\n", "284.53: 2\n", "63.04: 1\n", "87.95: 2\n", "222.72: 1\n", "314.92: 1\n", "389.82: 1\n", "152.31: 1\n", "316.74: 1\n", "66.2: 1\n", "228.1: 1\n", "40.48: 1\n", "90.2: 1\n", "90.93: 1\n", "167.03: 2\n", "213.26: 1\n", "53.83: 1\n", "68.51: 1\n", "13.95: 1\n", "73.6: 1\n", "172.08: 1\n", "50.57: 1\n", "23.76: 3\n", "108.85: 1\n", "98.65: 1\n", "75.62: 2\n", "35.97: 1\n", "77.98: 1\n", "91.34: 1\n", "168.88: 1\n", "377.96: 1\n", "83.96: 1\n", "10.69: 1\n", "130.54: 2\n", "107.67: 2\n", "47.75: 1\n", "35.96: 2\n", "124.73: 1\n", "140.22: 1\n", "43.05: 1\n", "108.11: 2\n", "90.55: 1\n", "11.32: 1\n", "355.2: 1\n", "51.49: 2\n", "159.54: 1\n", "128.98: 1\n", "297.55: 1\n", "18.6: 2\n", "613.12: 1\n", "294.46: 1\n", "99.86: 2\n", "111.51: 2\n", "25.76: 1\n", "126.24: 1\n", "27.9: 1\n", "407.35: 1\n", "115.74: 1\n", "118.73: 1\n", "67.53: 1\n", "18.51: 1\n", "24.34: 1\n", "107.71: 1\n", "88.53: 1\n", "29.99: 2\n", "22.58: 1\n", "110.53: 1\n", "93.82: 1\n", "275.67: 1\n", "353.55: 1\n", "207.67: 1\n", "22.24: 1\n", "255.9: 1\n", "332.31: 1\n", "86.45: 1\n", "133.14: 1\n", "278.9: 1\n", "108.26: 1\n", "25.18: 1\n", "185.89: 3\n", "63.95: 1\n", "188.15: 1\n", "147.18: 1\n", "288.8: 1\n", "87.09: 2\n", "59.86: 1\n", "61.04: 1\n", "149.65: 2\n", "142.63: 2\n", "171.43: 1\n", "56.75: 2\n", "104.81: 1\n", "121.55: 3\n", "264.77: 1\n", "53.18: 1\n", "18.68: 1\n", "12.82: 2\n", "115.86: 1\n", "286.45: 1\n", "131.0: 1\n", "400.65: 1\n", "169.53: 2\n", "47.22: 1\n", "412.54: 1\n", "374.98: 1\n", "179.54: 1\n", "713.31: 1\n", "22.89: 1\n", "336.64: 1\n", "36.02: 1\n", "159.17: 1\n", "78.35: 1\n", "88.41: 2\n", "115.32: 1\n", "258.77: 1\n", "193.78: 2\n", "41.39: 2\n", "77.11: 1\n", "62.33: 1\n", "305.86: 1\n", "59.29: 1\n", "53.74: 1\n", "83.75: 1\n", "5.36: 1\n", "22.59: 1\n", "61.11: 2\n", "241.59: 1\n", "98.85: 2\n", "89.5: 1\n", "340.59: 1\n", "262.72: 1\n", "314.82: 1\n", "27.26: 3\n", "23.75: 2\n", "82.26: 2\n", "239.71: 1\n", "174.42: 1\n", "228.21: 2\n", "93.02: 1\n", "9.92: 1\n", "124.16: 3\n", "80.15: 1\n", "90.15: 1\n", "340.11: 1\n", "210.37: 1\n", "25.87: 1\n", "184.49: 2\n", "27.59: 1\n", "357.28: 1\n", "50.75: 1\n", "25.55: 1\n", "77.81: 3\n", "118.91: 1\n", "166.28: 1\n", "25.73: 1\n", "91.64: 1\n", "116.04: 1\n", "65.87: 1\n", "279.6: 1\n", "449.02: 1\n", "123.51: 1\n", "178.53: 1\n", "207.9: 1\n", "175.06: 1\n", "27.29: 2\n", "136.07: 1\n", "166.66: 1\n", "86.13: 1\n", "53.49: 1\n", "151.62: 2\n", "292.04: 1\n", "336.33: 1\n", "49.31: 2\n", "30.17: 1\n", "191.77: 1\n", "69.42: 1\n", "370.98: 1\n", "159.26: 1\n", "22.03: 1\n", "252.91: 1\n", "385.04: 1\n", "166.24: 1\n", "168.64: 1\n", "127.01: 1\n", "165.42: 2\n", "191.64: 1\n", "422.96: 1\n", "239.54: 1\n", "89.86: 1\n", "140.01: 1\n", "152.67: 1\n", "18.26: 2\n", "96.1: 1\n", "209.02: 2\n", "556.8: 1\n", "317.17: 1\n", "118.36: 1\n", "71.63: 1\n", "46.81: 1\n", "364.7: 1\n", "59.46: 2\n", "134.76: 1\n", "38.01: 1\n", "109.07: 1\n", "224.3: 1\n", "158.27: 2\n", "127.96: 1\n", "134.89: 1\n", "185.84: 1\n", "99.75: 1\n", "197.05: 1\n", "64.8: 2\n", "268.88: 2\n", "183.21: 1\n", "124.54: 1\n", "133.71: 2\n", "124.34: 1\n", "111.82: 1\n", "35.78: 3\n", "73.29: 3\n", "450.15: 1\n", "54.95: 1\n", "402.9: 1\n", "87.59: 2\n", "281.4: 1\n", "36.95: 1\n", "292.21: 1\n", "61.78: 1\n", "105.12: 1\n", "49.86: 2\n", "114.33: 2\n", "85.81: 1\n", "53.34: 2\n", "506.61: 1\n", "72.19: 1\n", "399.35: 1\n", "508.34: 1\n", "252.23: 1\n", "39.57: 2\n", "156.07: 2\n", "360.21: 1\n", "193.21: 2\n", "102.92: 1\n", "238.28: 1\n", "82.74: 2\n", "127.33: 1\n", "45.46: 1\n", "20.16: 1\n", "365.03: 1\n", "18.38: 1\n", "91.62: 2\n", "22.61: 1\n", "115.59: 2\n", "75.31: 2\n", "73.88: 1\n", "108.61: 2\n", "211.12: 1\n", "44.53: 1\n", "629.99: 1\n", "92.13: 1\n", "20.2: 1\n", "12.81: 1\n", "214.93: 1\n", "279.04: 1\n", "4.47: 1\n", "232.67: 1\n", "76.21: 1\n", "546.74: 1\n", "154.9: 1\n", "223.21: 1\n", "145.43: 1\n", "191.56: 1\n", "101.05: 1\n", "50.6: 1\n", "33.35: 1\n", "103.18: 1\n", "150.27: 1\n", "465.14: 1\n", "175.64: 1\n", "319.5: 1\n", "135.7: 1\n", "93.49: 1\n", "211.15: 1\n", "103.46: 2\n", "23.49: 1\n", "302.17: 1\n", "262.01: 2\n", "336.89: 1\n", "402.02: 1\n", "36.72: 2\n", "36.64: 2\n", "20.57: 1\n", "282.96: 1\n", "245.92: 1\n", "374.32: 1\n", "241.55: 1\n", "338.43: 2\n", "40.73: 1\n", "94.76: 1\n", "191.81: 1\n", "58.66: 1\n", "36.66: 1\n", "196.55: 1\n", "360.41: 1\n", "396.97: 1\n", "351.93: 1\n", "24.17: 2\n", "19.16: 3\n", "90.24: 2\n", "148.63: 1\n", "38.44: 2\n", "42.42: 1\n", "282.38: 1\n", "162.84: 1\n", "157.44: 1\n", "143.33: 1\n", "38.02: 2\n", "89.16: 1\n", "114.64: 1\n", "302.99: 1\n", "12.94: 1\n", "49.2: 2\n", "14.31: 1\n", "27.74: 1\n", "46.52: 2\n", "32.16: 2\n", "196.73: 1\n", "29.85: 2\n", "44.0: 1\n", "31.35: 1\n", "110.38: 1\n", "49.06: 1\n", "141.07: 1\n", "63.33: 1\n", "119.77: 1\n", "131.19: 1\n", "56.45: 1\n", "43.16: 1\n", "77.59: 1\n", "157.59: 1\n", "11.85: 1\n", "68.73: 1\n", "150.64: 1\n", "69.06: 2\n", "289.08: 1\n", "73.23: 1\n", "181.78: 1\n", "388.15: 1\n", "129.22: 1\n", "255.24: 1\n", "343.8: 1\n", "326.16: 1\n", "144.45: 1\n", "274.67: 1\n", "224.95: 1\n", "108.9: 1\n", "201.01: 1\n", "444.91: 1\n", "251.93: 1\n", "252.51: 1\n", "240.71: 2\n", "319.76: 1\n", "430.73: 1\n", "120.05: 1\n", "279.38: 1\n", "448.53: 1\n", "68.1: 1\n", "304.91: 1\n", "263.96: 1\n", "87.45: 2\n", "169.76: 1\n", "372.25: 1\n", "198.93: 2\n", "343.51: 1\n", "313.97: 1\n", "169.03: 1\n", "118.42: 1\n", "102.98: 1\n", "205.5: 1\n", "446.77: 1\n", "304.35: 1\n", "229.25: 1\n", "238.05: 1\n", "131.87: 2\n", "567.48: 1\n", "313.06: 1\n", "186.58: 1\n", "137.71: 1\n", "200.37: 1\n", "142.91: 1\n", "119.68: 2\n", "65.81: 1\n", "70.79: 2\n", "61.25: 1\n", "137.01: 1\n", "140.85: 1\n", "32.56: 2\n", "82.4: 2\n", "207.86: 1\n", "21.47: 1\n", "88.96: 2\n", "98.87: 3\n", "253.13: 1\n", "41.04: 3\n", "108.6: 1\n", "157.34: 2\n", "226.72: 1\n", "532.15: 1\n", "130.62: 2\n", "282.01: 1\n", "57.55: 1\n", "75.67: 3\n", "129.27: 1\n", "113.57: 2\n", "105.98: 2\n", "61.86: 1\n", "31.56: 2\n", "83.69: 1\n", "180.48: 1\n", "134.37: 1\n", "23.16: 2\n", "437.63: 1\n", "144.29: 1\n", "39.02: 1\n", "92.78: 1\n", "175.3: 1\n", "36.71: 1\n", "199.46: 1\n", "438.55: 1\n", "399.21: 1\n", "224.72: 1\n", "106.92: 1\n", "81.38: 1\n", "344.27: 1\n", "45.63: 1\n", "36.67: 3\n", "74.63: 2\n", "82.89: 1\n", "97.68: 1\n", "249.31: 1\n", "49.76: 1\n", "49.47: 1\n", "20.98: 1\n", "86.12: 1\n", "21.06: 1\n", "113.03: 1\n", "114.29: 2\n", "17.17: 1\n", "386.74: 1\n", "73.86: 1\n", "20.24: 1\n", "37.4: 4\n", "62.53: 2\n", "241.94: 2\n", "483.13: 1\n", "254.5: 1\n", "25.37: 1\n", "79.57: 3\n", "89.82: 2\n", "69.95: 2\n", "376.35: 1\n", "86.32: 1\n", "22.85: 1\n", "346.44: 1\n", "387.64: 1\n", "137.12: 1\n", "148.73: 1\n", "49.51: 1\n", "26.84: 1\n", "142.51: 1\n", "153.2: 1\n", "339.33: 1\n", "482.4: 1\n", "18.77: 1\n", "209.86: 1\n", "61.27: 1\n", "17.15: 1\n", "186.15: 1\n", "23.98: 1\n", "25.0: 3\n", "30.33: 1\n", "363.81: 1\n", "62.76: 2\n", "50.31: 1\n", "195.54: 1\n", "363.02: 1\n", "155.37: 1\n", "78.0: 3\n", "219.82: 1\n", "312.68: 1\n", "89.55: 1\n", "32.68: 1\n", "124.65: 3\n", "48.18: 1\n", "187.8: 2\n", "140.05: 1\n", "48.82: 1\n", "115.83: 2\n", "29.42: 1\n", "48.58: 1\n", "308.0: 1\n", "181.71: 1\n", "79.78: 1\n", "144.96: 1\n", "48.56: 5\n", "111.21: 1\n", "74.86: 2\n", "92.75: 1\n", "182.58: 1\n", "68.56: 1\n", "164.17: 1\n", "79.04: 1\n", "430.99: 1\n", "100.25: 1\n", "163.89: 1\n", "280.31: 1\n", "193.36: 1\n", "64.15: 2\n", "100.64: 1\n", "118.15: 1\n", "65.41: 1\n", "90.01: 1\n", "42.36: 1\n", "103.82: 1\n", "126.33: 2\n", "365.34: 1\n", "154.37: 2\n", "21.44: 3\n", "14.07: 1\n", "506.89: 1\n", "58.61: 1\n", "245.15: 1\n", "8.81: 1\n", "87.98: 1\n", "289.94: 2\n", "80.53: 1\n", "80.03: 1\n", "81.63: 2\n", "175.6: 1\n", "41.7: 1\n", "225.7: 2\n", "198.91: 2\n", "38.9: 2\n", "169.88: 1\n", "51.03: 1\n", "154.57: 1\n", "84.97: 1\n", "145.84: 1\n", "52.83: 2\n", "129.18: 2\n", "97.71: 1\n", "390.43: 1\n", "169.06: 1\n", "181.55: 2\n", "163.3: 1\n", "37.56: 1\n", "114.6: 1\n", "58.25: 1\n", "40.42: 1\n", "237.86: 2\n", "94.53: 1\n", "453.01: 1\n", "82.3: 1\n", "97.15: 2\n", "311.2: 1\n", "145.2: 2\n", "210.09: 1\n", "215.37: 1\n", "327.9: 1\n", "19.61: 3\n", "160.18: 1\n", "30.92: 1\n", "389.94: 2\n", "339.86: 1\n", "320.84: 1\n", "90.88: 1\n", "56.03: 2\n", "494.42: 1\n", "38.12: 1\n", "18.73: 1\n", "85.69: 2\n", "509.88: 1\n", "15.54: 1\n", "62.34: 1\n", "175.22: 1\n", "20.45: 1\n", "107.2: 1\n", "104.21: 1\n", "29.56: 1\n", "148.43: 2\n", "93.43: 1\n", "55.97: 1\n", "39.76: 1\n", "269.19: 1\n", "77.71: 1\n", "282.02: 1\n", "77.61: 1\n", "464.01: 1\n", "151.7: 1\n", "40.92: 1\n", "83.93: 1\n", "29.83: 2\n", "373.36: 1\n", "143.72: 1\n", "380.04: 1\n", "82.83: 1\n", "70.03: 1\n", "88.36: 1\n", "198.66: 1\n", "74.76: 1\n", "436.51: 1\n", "295.06: 1\n", "29.38: 2\n", "131.74: 2\n", "129.25: 1\n", "24.16: 1\n", "50.98: 1\n", "128.44: 1\n", "44.33: 2\n", "128.09: 1\n", "184.04: 1\n", "222.36: 1\n", "24.03: 1\n", "236.69: 1\n", "107.66: 1\n", "67.71: 2\n", "53.31: 1\n", "187.23: 1\n", "40.06: 1\n", "123.03: 1\n", "47.14: 2\n", "48.37: 1\n", "116.28: 3\n", "139.72: 1\n", "350.65: 1\n", "60.75: 1\n", "58.72: 1\n", "155.22: 1\n", "141.94: 1\n", "23.64: 1\n", "270.6: 1\n", "139.1: 2\n", "229.73: 1\n", "32.73: 1\n", "39.22: 1\n", "57.63: 2\n", "359.71: 1\n", "104.28: 1\n", "80.2: 1\n", "114.75: 1\n", "9.18: 1\n", "58.65: 1\n", "278.11: 1\n", "96.09: 1\n", "68.36: 1\n", "53.32: 2\n", "52.31: 1\n", "64.32: 3\n", "100.66: 1\n", "72.37: 2\n", "280.12: 2\n", "75.3: 1\n", "51.68: 2\n", "558.42: 1\n", "49.26: 2\n", "203.01: 2\n", "119.58: 1\n", "427.95: 1\n", "182.8: 1\n", "262.31: 1\n", "231.6: 1\n", "206.68: 1\n", "51.88: 2\n", "80.39: 1\n", "117.55: 2\n", "164.49: 3\n", "212.54: 1\n", "246.7: 1\n", "110.0: 1\n", "28.6: 1\n", "337.25: 1\n", "169.59: 1\n", "72.6: 1\n", "224.15: 1\n", "212.71: 1\n", "192.4: 1\n", "407.06: 1\n", "67.65: 1\n", "119.84: 1\n", "121.79: 1\n", "93.12: 1\n", "232.33: 1\n", "127.99: 1\n", "155.89: 1\n", "91.73: 2\n", "47.71: 2\n", "248.75: 2\n", "5.97: 1\n", "297.85: 2\n", "25.14: 2\n", "233.03: 2\n", "135.45: 1\n", "182.76: 1\n", "315.69: 1\n", "145.06: 1\n", "82.85: 1\n", "122.91: 1\n", "118.53: 1\n", "26.4: 2\n", "78.45: 3\n", "362.07: 1\n", "154.11: 1\n", "225.91: 1\n", "133.96: 1\n", "125.56: 2\n", "19.45: 1\n", "333.55: 1\n", "242.23: 1\n", "96.76: 1\n", "41.99: 1\n", "185.74: 1\n", "124.52: 2\n", "153.72: 1\n", "299.66: 1\n", "98.76: 1\n", "54.51: 2\n", "141.23: 1\n", "45.77: 1\n", "267.96: 1\n", "132.11: 1\n", "54.03: 3\n", "309.09: 1\n", "116.85: 1\n", "375.21: 1\n", "66.52: 3\n", "133.61: 1\n", "108.84: 1\n", "394.13: 1\n", "63.81: 1\n", "178.6: 1\n", "33.96: 1\n", "254.73: 1\n", "43.75: 1\n", "107.36: 1\n", "32.5: 1\n", "71.65: 1\n", "346.12: 1\n", "58.47: 2\n", "19.94: 1\n", "224.25: 1\n", "154.67: 1\n", "75.94: 1\n", "156.85: 1\n", "90.83: 1\n", "181.38: 1\n", "155.21: 1\n", "26.81: 1\n", "40.74: 1\n", "46.01: 1\n", "238.7: 1\n", "66.25: 1\n", "18.35: 3\n", "46.86: 2\n", "133.73: 1\n", "272.66: 1\n", "79.16: 1\n", "44.92: 1\n", "107.53: 2\n", "100.41: 1\n", "194.3: 1\n", "42.51: 1\n", "221.45: 1\n", "131.17: 1\n", "137.04: 1\n", "122.98: 2\n", "157.5: 1\n", "102.52: 1\n", "200.08: 1\n", "90.57: 2\n", "167.02: 1\n", "120.53: 1\n", "109.01: 1\n", "237.99: 3\n", "86.6: 1\n", "108.59: 1\n", "43.59: 1\n", "186.19: 1\n", "139.42: 1\n", "37.32: 1\n", "111.58: 1\n", "353.3: 1\n", "286.8: 1\n", "56.32: 1\n", "271.36: 1\n", "77.9: 1\n", "53.92: 1\n", "28.1: 2\n", "133.02: 2\n", "86.82: 2\n", "223.04: 1\n", "280.91: 1\n", "87.3: 2\n", "164.32: 1\n", "76.95: 1\n", "84.59: 1\n", "160.1: 3\n", "248.99: 1\n", "254.46: 1\n", "90.9: 2\n", "34.04: 1\n", "213.37: 1\n", "331.0: 1\n", "68.69: 3\n", "41.32: 1\n", "117.46: 1\n", "137.25: 1\n", "170.42: 1\n", "287.75: 1\n", "170.66: 1\n", "407.94: 1\n", "102.11: 2\n", "189.11: 2\n", "171.56: 1\n", "487.86: 1\n", "32.61: 1\n", "129.64: 1\n", "239.73: 1\n", "219.76: 1\n", "78.38: 1\n", "256.41: 1\n", "87.16: 2\n", "160.76: 1\n", "267.0: 1\n", "205.02: 2\n", "219.73: 1\n", "63.73: 1\n", "123.28: 1\n", "19.75: 1\n", "60.84: 1\n", "100.34: 1\n", "42.25: 1\n", "128.74: 1\n", "166.77: 2\n", "7.55: 1\n", "55.75: 1\n", "204.45: 1\n", "211.27: 1\n", "41.57: 1\n", "354.45: 1\n", "72.12: 1\n", "294.83: 1\n", "21.59: 2\n", "44.25: 1\n", "43.53: 1\n", "42.27: 1\n", "56.82: 2\n", "188.84: 1\n", "21.75: 1\n", "37.39: 2\n", "25.67: 1\n", "184.52: 1\n", "359.51: 1\n", "25.94: 2\n", "162.66: 1\n", "43.84: 2\n", "219.77: 1\n", "54.86: 3\n", "98.81: 1\n", "277.31: 1\n", "226.29: 1\n", "37.11: 1\n", "174.2: 3\n", "527.73: 1\n", "40.64: 2\n", "91.28: 1\n", "124.37: 1\n", "57.39: 1\n", "61.17: 1\n", "15.59: 1\n", "251.71: 1\n", "65.58: 2\n", "121.75: 2\n", "150.85: 1\n", "106.71: 1\n", "225.02: 1\n", "274.12: 1\n", "326.44: 1\n", "9.16: 1\n", "150.05: 2\n", "388.18: 1\n", "220.6: 1\n", "27.16: 1\n", "353.32: 1\n", "291.45: 1\n", "226.52: 1\n", "94.97: 3\n", "44.63: 1\n", "81.99: 1\n", "124.6: 1\n", "119.17: 2\n", "16.54: 2\n", "194.89: 1\n", "17.42: 1\n", "58.63: 1\n", "123.32: 1\n", "137.06: 1\n", "53.05: 5\n", "59.48: 1\n", "67.62: 2\n", "269.87: 1\n", "37.06: 1\n", "167.49: 2\n", "137.67: 1\n", "18.98: 1\n", "45.2: 1\n", "321.32: 1\n", "318.1: 1\n", "136.05: 1\n", "469.19: 1\n", "39.35: 1\n", "28.2: 1\n", "496.55: 1\n", "15.25: 1\n", "173.09: 1\n", "248.8: 1\n", "277.34: 1\n", "73.19: 1\n", "152.17: 2\n", "55.9: 1\n", "57.49: 1\n", "32.62: 1\n", "17.14: 1\n", "214.08: 1\n", "65.09: 1\n", "140.7: 1\n", "32.41: 1\n", "285.48: 1\n", "103.63: 1\n", "225.18: 2\n", "74.68: 2\n", "285.91: 1\n", "140.62: 1\n", "54.36: 1\n", "15.33: 1\n", "73.9: 2\n", "106.72: 1\n", "27.1: 1\n", "32.19: 2\n", "37.46: 1\n", "13.6: 1\n", "235.0: 1\n", "547.23: 1\n", "54.69: 1\n", "415.62: 1\n", "23.09: 1\n", "39.33: 1\n", "277.39: 1\n", "139.06: 1\n", "21.79: 1\n", "106.02: 1\n", "49.82: 1\n", "28.86: 2\n", "141.75: 2\n", "289.5: 1\n", "339.68: 1\n", "250.37: 1\n", "291.78: 1\n", "84.81: 1\n", "255.55: 1\n", "146.74: 2\n", "161.95: 1\n", "347.28: 1\n", "34.37: 2\n", "5.11: 1\n", "125.81: 1\n", "76.72: 1\n", "53.29: 1\n", "161.34: 1\n", "277.09: 1\n", "94.05: 2\n", "15.02: 1\n", "339.1: 1\n", "288.9: 1\n", "399.23: 1\n", "83.31: 1\n", "338.1: 1\n", "55.01: 2\n", "176.12: 1\n", "215.98: 1\n", "93.99: 2\n", "82.39: 1\n", "316.61: 1\n", "220.41: 1\n", "57.92: 1\n", "387.45: 1\n", "122.25: 1\n", "58.05: 1\n", "290.42: 1\n", "123.5: 1\n", "86.83: 2\n", "221.85: 1\n", "25.83: 1\n", "106.4: 1\n", "259.62: 1\n", "60.86: 1\n", "146.14: 1\n", "81.29: 1\n", "113.37: 1\n", "126.91: 2\n", "201.07: 1\n", "230.76: 1\n", "41.85: 1\n", "159.56: 1\n", "157.77: 1\n", "237.2: 2\n", "157.15: 1\n", "72.15: 2\n", "178.17: 1\n", "73.09: 1\n", "161.41: 1\n", "86.22: 1\n", "525.74: 1\n", "58.06: 2\n", "353.46: 1\n", "84.73: 1\n", "23.33: 3\n", "293.34: 1\n", "75.2: 2\n", "65.27: 1\n", "99.0: 1\n", "141.88: 1\n", "53.6: 1\n", "306.52: 1\n", "158.74: 1\n", "115.87: 1\n", "81.75: 1\n", "98.68: 2\n", "273.8: 1\n", "182.38: 1\n", "290.85: 1\n", "122.83: 2\n", "86.41: 2\n", "170.02: 1\n", "56.49: 2\n", "169.22: 1\n", "54.99: 1\n", "77.03: 1\n", "306.51: 1\n", "258.41: 1\n", "16.15: 2\n", "103.2: 1\n", "377.93: 1\n", "195.88: 1\n", "121.74: 1\n", "114.2: 1\n", "226.86: 1\n", "162.98: 1\n", "53.15: 1\n", "276.19: 1\n", "30.29: 1\n", "57.05: 2\n", "30.94: 3\n", "40.14: 1\n", "31.68: 1\n", "206.99: 1\n", "15.22: 1\n", "110.31: 1\n", "108.19: 1\n", "217.21: 1\n", "96.75: 1\n", "275.86: 1\n", "216.58: 1\n", "168.51: 1\n", "46.25: 2\n", "249.63: 1\n", "63.09: 1\n", "201.17: 1\n", "287.07: 1\n", "78.25: 1\n", "47.29: 2\n", "256.88: 1\n", "199.11: 1\n", "142.67: 1\n", "259.92: 1\n", "287.44: 1\n", "443.25: 1\n", "444.2: 1\n", "73.65: 1\n", "227.87: 1\n", "149.26: 2\n", "20.78: 1\n", "61.97: 2\n", "127.56: 1\n", "333.0: 1\n", "146.65: 1\n", "469.33: 1\n", "139.97: 2\n", "30.62: 1\n", "113.8: 1\n", "142.88: 1\n", "169.28: 1\n", "373.94: 1\n", "353.94: 1\n", "143.59: 1\n", "102.38: 1\n", "231.96: 1\n", "23.04: 2\n", "87.27: 1\n", "650.09: 1\n", "36.3: 1\n", "61.61: 1\n", "172.69: 1\n", "144.63: 1\n", "79.65: 1\n", "232.19: 1\n", "54.88: 1\n", "173.88: 1\n", "45.59: 1\n", "98.75: 1\n", "198.29: 1\n", "60.48: 1\n", "176.06: 1\n", "70.95: 1\n", "25.22: 1\n", "176.64: 1\n", "203.3: 1\n", "166.62: 1\n", "30.75: 2\n", "469.5: 1\n", "82.29: 1\n", "252.72: 1\n", "22.14: 1\n", "54.15: 1\n", "281.33: 1\n", "247.8: 1\n", "299.2: 1\n", "236.2: 1\n", "157.48: 1\n", "107.05: 1\n", "16.12: 3\n", "89.38: 4\n", "88.97: 1\n", "110.68: 1\n", "108.72: 1\n", "314.02: 1\n", "293.35: 1\n", "131.12: 1\n", "251.43: 1\n", "225.37: 1\n", "82.88: 1\n", "270.77: 1\n", "83.1: 2\n", "146.52: 1\n", "133.38: 1\n", "103.3: 1\n", "128.82: 2\n", "215.49: 1\n", "131.33: 1\n", "66.56: 1\n", "253.14: 1\n", "16.28: 1\n", "65.55: 2\n", "24.42: 2\n", "56.18: 1\n", "149.1: 1\n", "182.73: 1\n", "43.51: 2\n", "109.63: 3\n", "57.36: 1\n", "443.17: 1\n", "70.45: 1\n", "124.2: 1\n", "331.5: 1\n", "325.31: 1\n", "18.54: 2\n", "366.67: 1\n", "63.26: 1\n", "514.58: 1\n", "105.55: 1\n", "78.92: 1\n", "84.37: 1\n", "269.76: 1\n", "86.87: 2\n", "33.09: 2\n", "342.73: 1\n", "19.04: 2\n", "80.49: 1\n", "150.44: 1\n", "36.37: 1\n", "101.08: 1\n", "170.17: 1\n", "204.19: 1\n", "147.37: 1\n", "145.12: 1\n", "71.19: 1\n", "88.68: 1\n", "160.65: 2\n", "184.41: 1\n", "92.61: 2\n", "7.64: 1\n", "313.62: 1\n", "355.7: 1\n", "207.71: 1\n", "84.16: 1\n", "38.33: 1\n", "178.48: 1\n", "195.87: 1\n", "156.8: 1\n", "430.88: 1\n", "53.48: 1\n", "51.24: 1\n", "173.78: 1\n", "115.82: 2\n", "22.46: 1\n", "245.84: 2\n", "172.19: 1\n", "131.39: 1\n", "318.47: 1\n", "306.69: 1\n", "30.71: 1\n", "182.62: 1\n", "104.73: 1\n", "103.75: 1\n", "94.79: 1\n", "66.42: 1\n", "184.86: 1\n", "132.81: 1\n", "176.07: 1\n", "147.59: 1\n", "255.99: 1\n", "253.76: 1\n", "107.91: 1\n", "15.7: 1\n", "64.02: 1\n", "250.21: 1\n", "137.95: 1\n", "11.18: 1\n", "162.62: 1\n", "67.01: 1\n", "48.6: 2\n", "236.83: 1\n", "52.5: 1\n", "104.41: 1\n", "169.45: 1\n", "49.21: 1\n", "34.13: 1\n", "177.29: 1\n", "102.51: 1\n", "55.59: 2\n", "140.84: 1\n", "81.68: 1\n", "75.77: 2\n", "122.44: 1\n", "123.05: 1\n", "64.26: 1\n", "257.22: 1\n", "452.51: 1\n", "292.97: 1\n", "183.5: 1\n", "44.36: 1\n", "103.74: 1\n", "121.11: 1\n", "150.41: 1\n", "84.25: 1\n", "47.77: 1\n", "63.63: 1\n", "505.43: 1\n", "146.43: 1\n", "53.82: 2\n", "166.67: 1\n", "150.18: 1\n", "483.91: 1\n", "55.69: 1\n", "27.8: 2\n", "166.99: 1\n", "127.42: 1\n", "334.71: 1\n", "282.35: 1\n", "47.36: 1\n", "74.6: 1\n", "57.14: 1\n", "373.96: 1\n", "291.49: 1\n", "127.31: 1\n", "180.46: 1\n", "148.68: 1\n", "28.41: 1\n", "115.46: 1\n", "192.18: 1\n", "689.65: 1\n", "227.3: 1\n", "310.89: 1\n", "102.8: 1\n", "260.34: 1\n", "131.7: 1\n", "198.67: 2\n", "305.89: 1\n", "11.61: 1\n", "345.61: 1\n", "275.64: 1\n", "247.35: 1\n", "81.43: 1\n", "111.73: 1\n", "91.61: 1\n", "223.37: 1\n", "196.39: 1\n", "49.36: 4\n", "360.61: 1\n", "105.59: 2\n", "153.02: 1\n", "274.47: 1\n", "29.54: 1\n", "146.86: 1\n", "703.23: 1\n", "576.48: 1\n", "69.61: 3\n", "73.79: 2\n", "458.12: 1\n", "278.25: 1\n", "434.2: 1\n", "271.77: 1\n", "82.5: 2\n", "41.45: 1\n", "305.01: 1\n", "272.0: 1\n", "85.84: 1\n", "22.73: 1\n", "735.19: 1\n", "454.29: 1\n", "95.56: 1\n", "80.13: 2\n", "146.35: 1\n", "29.49: 1\n", "56.83: 1\n", "263.54: 1\n", "334.87: 1\n", "167.67: 1\n", "168.43: 1\n", "365.67: 1\n", "342.72: 1\n", "298.59: 1\n", "207.83: 1\n", "527.07: 1\n", "143.77: 1\n", "251.63: 1\n", "227.07: 2\n", "245.69: 1\n", "20.87: 1\n", "216.46: 1\n", "17.57: 1\n", "130.35: 1\n", "139.24: 1\n", "147.25: 1\n", "67.34: 2\n", "151.21: 1\n", "81.65: 1\n", "232.09: 1\n", "99.36: 1\n", "646.59: 1\n", "70.29: 1\n", "217.33: 1\n", "463.86: 1\n", "272.67: 1\n", "151.54: 1\n", "456.17: 1\n", "239.39: 1\n", "60.07: 1\n", "10.41: 1\n", "248.27: 1\n", "326.61: 1\n", "146.93: 1\n", "23.71: 1\n", "47.88: 2\n", "334.28: 1\n", "74.44: 1\n", "48.99: 1\n", "373.42: 1\n", "202.61: 1\n", "289.0: 1\n", "69.77: 1\n", "48.85: 1\n", "192.33: 1\n", "27.86: 2\n", "106.26: 1\n", "79.17: 1\n", "44.42: 1\n", "40.86: 1\n", "152.91: 1\n", "59.53: 4\n", "51.4: 1\n", "47.42: 1\n", "34.38: 1\n", "109.46: 1\n", "158.2: 1\n", "112.33: 2\n", "149.4: 1\n", "314.48: 1\n", "105.43: 2\n", "326.34: 2\n", "152.78: 1\n", "32.95: 1\n", "123.22: 1\n", "65.05: 1\n", "283.68: 1\n", "359.15: 1\n", "35.41: 2\n", "41.65: 1\n", "476.3: 1\n", "237.03: 2\n", "33.22: 1\n", "92.33: 1\n", "271.92: 1\n", "257.2: 1\n", "239.28: 1\n", "137.02: 1\n", "51.07: 1\n", "396.68: 1\n", "90.66: 1\n", "89.69: 2\n", "183.93: 1\n", "287.93: 1\n", "539.68: 1\n", "220.45: 1\n", "249.87: 1\n", "65.3: 3\n", "204.95: 1\n", "11.42: 1\n", "339.24: 1\n", "163.19: 2\n", "154.29: 1\n", "138.78: 1\n", "612.48: 1\n", "244.15: 1\n", "198.46: 1\n", "46.5: 1\n", "61.91: 1\n", "15.38: 1\n", "633.21: 1\n", "52.45: 1\n", "211.7: 1\n", "499.74: 1\n", "217.18: 1\n", "243.09: 1\n", "210.03: 1\n", "37.43: 2\n", "74.13: 2\n", "137.19: 1\n", "502.75: 1\n", "108.94: 1\n", "168.44: 1\n", "32.35: 1\n", "192.72: 1\n", "82.23: 1\n", "386.78: 1\n", "136.55: 1\n", "119.69: 1\n", "287.81: 1\n", "383.29: 1\n", "128.47: 1\n", "256.53: 1\n", "165.08: 1\n", "323.06: 1\n", "226.69: 1\n", "247.44: 1\n", "32.78: 2\n", "233.96: 1\n", "41.78: 1\n", "98.72: 1\n", "326.9: 1\n", "311.73: 1\n", "292.41: 1\n", "92.52: 1\n", "22.19: 1\n", "319.17: 1\n", "27.42: 1\n", "615.39: 1\n", "389.9: 1\n", "306.09: 1\n", "74.47: 1\n", "333.1: 1\n", "73.91: 2\n", "318.39: 1\n", "146.07: 1\n", "137.21: 1\n", "51.74: 2\n", "202.46: 2\n", "26.18: 1\n", "166.57: 1\n", "316.06: 1\n", "42.79: 2\n", "183.19: 1\n", "198.34: 1\n", "263.98: 1\n", "707.26: 1\n", "52.78: 1\n", "14.73: 4\n", "183.66: 1\n", "51.56: 1\n", "167.8: 1\n", "134.21: 1\n", "223.39: 1\n", "117.54: 1\n", "174.06: 1\n", "126.65: 1\n", "37.07: 2\n", "47.84: 1\n", "186.28: 1\n", "295.89: 1\n", "149.6: 1\n", "180.16: 1\n", "75.79: 1\n", "491.56: 1\n", "189.91: 1\n", "42.2: 2\n", "134.19: 1\n", "37.29: 2\n", "267.23: 1\n", "187.94: 1\n", "69.81: 1\n", "62.55: 1\n", "391.88: 1\n", "113.35: 1\n", "70.02: 1\n", "38.37: 1\n", "181.69: 2\n", "67.23: 2\n", "460.96: 1\n", "57.27: 1\n", "294.9: 1\n", "17.73: 1\n", "132.61: 2\n", "93.55: 2\n", "101.16: 1\n", "25.57: 1\n", "17.62: 1\n", "41.64: 1\n", "20.08: 1\n", "716.49: 1\n", "94.71: 1\n", "133.42: 1\n", "180.86: 1\n", "296.39: 1\n", "482.64: 1\n", "251.95: 1\n", "242.64: 1\n", "297.03: 1\n", "134.38: 1\n", "456.16: 2\n", "62.37: 1\n", "94.92: 1\n", "295.25: 1\n", "190.61: 1\n", "101.15: 4\n", "266.22: 1\n", "129.49: 1\n", "115.71: 1\n", "621.58: 1\n", "97.11: 1\n", "102.7: 3\n", "141.5: 1\n", "341.32: 1\n", "47.8: 2\n", "182.4: 1\n", "34.42: 1\n", "58.19: 2\n", "127.68: 1\n", "110.6: 1\n", "86.2: 1\n", "682.25: 1\n", "108.54: 1\n", "153.99: 1\n", "123.52: 2\n", "413.14: 1\n", "41.22: 3\n", "83.22: 1\n", "218.71: 1\n", "55.57: 1\n", "73.01: 1\n", "202.54: 1\n", "143.22: 1\n", "90.25: 1\n", "316.07: 1\n", "46.24: 1\n", "336.46: 1\n", "23.05: 2\n", "116.6: 1\n", "32.13: 1\n", "349.85: 1\n", "38.11: 2\n", "282.87: 1\n", "179.85: 1\n", "450.07: 1\n", "237.23: 2\n", "79.26: 4\n", "113.6: 2\n", "260.17: 1\n", "167.35: 2\n", "38.38: 1\n", "97.45: 1\n", "174.39: 2\n", "38.0: 1\n", "208.73: 1\n", "152.19: 1\n", "104.3: 1\n", "278.54: 1\n", "44.2: 1\n", "49.01: 1\n", "192.84: 2\n", "497.07: 1\n", "213.88: 1\n", "318.69: 1\n", "249.85: 3\n", "151.29: 1\n", "125.62: 1\n", "96.13: 1\n", "178.86: 1\n", "96.59: 2\n", "188.1: 1\n", "171.9: 1\n", "101.6: 1\n", "24.6: 1\n", "48.62: 2\n", "345.37: 1\n", "73.62: 2\n", "152.8: 1\n", "250.13: 1\n", "144.74: 2\n", "191.72: 1\n", "148.7: 1\n", "99.18: 1\n", "112.85: 1\n", "477.55: 1\n", "343.63: 1\n", "96.17: 1\n", "221.4: 2\n", "17.95: 2\n", "200.22: 1\n", "267.81: 1\n", "99.94: 2\n", "179.45: 1\n", "43.29: 1\n", "187.78: 1\n", "172.77: 1\n", "64.88: 1\n", "171.54: 2\n", "354.42: 1\n", "41.37: 2\n", "46.46: 2\n", "181.97: 1\n", "105.05: 1\n", "314.18: 1\n", "79.94: 1\n", "49.54: 2\n", "48.05: 3\n", "253.91: 1\n", "229.57: 1\n", "219.66: 1\n", "48.78: 1\n", "40.43: 1\n", "143.41: 2\n", "120.35: 1\n", "272.44: 1\n", "164.02: 1\n", "31.84: 1\n", "128.9: 2\n", "351.69: 1\n", "81.81: 1\n", "32.89: 1\n", "128.15: 1\n", "24.51: 2\n", "88.39: 1\n", "127.63: 1\n", "158.04: 2\n", "388.66: 1\n", "675.66: 1\n", "158.5: 1\n", "94.26: 2\n", "50.79: 1\n", "99.05: 1\n", "168.52: 2\n", "112.2: 2\n", "72.03: 2\n", "240.09: 1\n", "238.21: 1\n", "193.54: 1\n", "109.92: 1\n", "594.45: 1\n", "69.55: 2\n", "42.85: 2\n", "51.77: 2\n", "112.34: 1\n", "233.23: 1\n", "442.5: 1\n", "30.21: 1\n", "103.19: 2\n", "163.16: 1\n", "200.82: 1\n", "72.21: 2\n", "44.29: 1\n", "102.3: 1\n", "42.78: 2\n", "241.81: 1\n", "200.02: 1\n", "171.26: 1\n", "74.48: 2\n", "132.19: 1\n", "253.53: 1\n", "114.37: 1\n", "547.71: 1\n", "281.66: 1\n", "82.69: 2\n", "236.59: 1\n", "143.71: 1\n", "56.67: 2\n", "76.39: 3\n", "68.06: 1\n", "53.62: 1\n", "477.4: 1\n", "138.71: 1\n", "471.21: 1\n", "125.54: 1\n", "177.27: 1\n", "201.02: 1\n", "61.67: 1\n", "220.64: 1\n", "188.22: 1\n", "110.2: 2\n", "375.1: 1\n", "333.34: 1\n", "192.13: 1\n", "119.14: 1\n", "66.0: 1\n", "235.91: 1\n", "275.81: 1\n", "98.2: 1\n", "185.68: 1\n", "307.77: 1\n", "208.81: 2\n", "64.35: 1\n", "175.99: 2\n", "55.06: 1\n", "57.37: 2\n", "51.86: 1\n", "128.03: 1\n", "213.9: 2\n", "603.57: 1\n", "435.84: 1\n", "83.29: 2\n", "52.35: 1\n", "290.04: 1\n", "51.21: 2\n", "43.78: 4\n", "41.23: 1\n", "293.48: 1\n", "86.85: 1\n", "52.49: 1\n", "507.77: 1\n", "83.02: 1\n", "123.79: 1\n", "141.37: 2\n", "68.88: 3\n", "273.55: 1\n", "500.92: 1\n", "150.78: 2\n", "24.99: 1\n", "118.24: 1\n", "160.64: 1\n", "195.37: 1\n", "119.38: 1\n", "161.21: 1\n", "496.38: 1\n", "169.41: 1\n", "229.27: 1\n", "112.81: 1\n", "470.8: 1\n", "17.58: 1\n", "163.35: 1\n", "183.69: 1\n", "35.61: 1\n", "237.21: 1\n", "358.05: 1\n", "157.17: 1\n", "64.62: 1\n", "504.3: 1\n", "230.12: 1\n", "141.06: 1\n", "145.14: 1\n", "138.6: 1\n", "253.99: 1\n", "31.18: 1\n", "235.58: 1\n", "34.72: 2\n", "73.28: 1\n", "478.37: 1\n", "88.92: 1\n", "268.17: 1\n", "337.4: 1\n", "127.12: 3\n", "148.93: 1\n", "86.25: 1\n", "148.76: 1\n", "20.39: 1\n", "367.11: 1\n", "129.87: 1\n", "358.17: 1\n", "28.93: 1\n", "129.8: 1\n", "18.13: 2\n", "346.11: 1\n", "119.74: 1\n", "397.66: 1\n", "229.48: 1\n", "31.41: 1\n", "81.2: 1\n", "168.01: 1\n", "42.45: 2\n", "163.59: 1\n", "19.98: 2\n", "33.91: 1\n", "196.23: 1\n", "357.59: 1\n", "179.03: 1\n", "128.38: 1\n", "24.59: 1\n", "51.26: 1\n", "18.1: 3\n", "429.75: 1\n", "122.53: 1\n", "58.99: 1\n", "191.49: 1\n", "203.78: 1\n", "362.91: 1\n", "244.27: 1\n", "304.58: 1\n", "64.48: 1\n", "284.52: 1\n", "29.62: 2\n", "172.56: 3\n", "111.09: 1\n", "130.57: 1\n", "451.4: 1\n", "51.89: 1\n", "162.21: 1\n", "20.19: 1\n", "295.53: 1\n", "355.66: 1\n", "241.3: 1\n", "67.24: 1\n", "224.4: 1\n", "29.73: 1\n", "329.93: 1\n", "161.15: 1\n", "31.7: 1\n", "19.64: 1\n", "406.13: 1\n", "45.82: 1\n", "585.3: 1\n", "560.04: 1\n", "215.42: 1\n", "91.74: 1\n", "177.59: 1\n", "106.27: 1\n", "33.86: 1\n", "265.11: 1\n", "250.99: 1\n", "322.24: 1\n", "115.25: 2\n", "224.06: 1\n", "54.0: 1\n", "355.99: 1\n", "191.17: 1\n", "212.37: 1\n", "309.49: 1\n", "99.97: 1\n", "330.35: 1\n", "460.31: 1\n", "88.98: 2\n", "137.78: 1\n", "219.98: 1\n", "78.54: 2\n", "37.98: 1\n", "71.69: 1\n", "494.68: 1\n", "124.3: 1\n", "46.33: 2\n", "320.95: 1\n", "86.44: 1\n", "244.23: 2\n", "87.11: 1\n", "30.76: 3\n", "32.82: 2\n", "34.06: 1\n", "229.09: 1\n", "220.4: 1\n", "7.17: 1\n", "105.24: 2\n", "90.18: 1\n", "46.92: 1\n", "170.83: 1\n", "320.41: 1\n", "220.83: 2\n", "173.79: 3\n", "231.2: 1\n", "93.88: 1\n", "412.61: 1\n", "108.65: 1\n", "5.55: 1\n", "158.84: 1\n", "188.68: 1\n", "80.61: 2\n", "615.2: 1\n", "52.75: 1\n", "245.36: 1\n", "69.2: 1\n", "216.74: 1\n", "384.32: 2\n", "269.63: 1\n", "95.66: 1\n", "268.65: 1\n", "31.0: 1\n", "74.8: 1\n", "139.96: 1\n", "192.64: 1\n", "347.64: 1\n", "61.43: 2\n", "219.28: 1\n", "353.8: 1\n", "261.0: 2\n", "140.91: 2\n", "31.2: 1\n", "451.84: 1\n", "29.74: 1\n", "490.71: 1\n", "185.83: 1\n", "115.06: 1\n", "268.73: 1\n", "296.45: 1\n", "114.74: 1\n", "190.1: 1\n", "64.97: 1\n", "197.01: 1\n", "161.6: 2\n", "144.06: 1\n", "466.33: 1\n", "189.79: 1\n", "363.46: 1\n", "551.9: 1\n", "153.06: 2\n", "397.23: 1\n", "374.56: 2\n", "183.58: 1\n", "183.04: 1\n", "80.11: 1\n", "137.91: 1\n", "53.89: 1\n", "79.12: 1\n", "505.75: 1\n", "391.35: 1\n", "185.6: 2\n", "215.09: 1\n", "39.44: 2\n", "70.0: 1\n", "36.91: 2\n", "254.31: 1\n", "247.62: 1\n", "175.04: 1\n", "399.71: 1\n", "5.38: 2\n", "184.29: 1\n", "109.82: 2\n", "198.69: 2\n", "138.22: 1\n", "342.75: 1\n", "32.9: 1\n", "193.81: 1\n", "173.87: 1\n", "221.19: 1\n", "139.45: 2\n", "406.27: 1\n", "34.03: 2\n", "170.86: 1\n", "60.87: 1\n", "89.48: 3\n", "195.78: 1\n", "60.33: 1\n", "17.83: 1\n", "57.26: 1\n", "246.66: 1\n", "322.47: 1\n", "166.8: 1\n", "196.22: 1\n", "57.28: 1\n", "409.35: 1\n", "356.87: 1\n", "199.03: 1\n", "71.31: 1\n", "376.96: 1\n", "455.63: 1\n", "257.87: 1\n", "117.5: 2\n", "492.13: 1\n", "271.72: 1\n", "176.27: 1\n", "221.32: 1\n", "249.14: 1\n", "59.6: 1\n", "393.36: 1\n", "49.89: 1\n", "155.52: 1\n", "76.48: 1\n", "183.25: 1\n", "537.5: 1\n", "26.77: 1\n", "24.55: 1\n", "119.56: 1\n", "90.87: 1\n", "86.23: 1\n", "76.41: 1\n", "141.15: 1\n", "121.4: 1\n", "84.13: 1\n", "51.12: 1\n", "95.83: 1\n", "184.7: 1\n", "95.3: 1\n", "123.91: 1\n", "32.23: 1\n", "44.47: 1\n", "130.45: 1\n", "29.4: 1\n", "452.54: 1\n", "132.86: 1\n", "176.87: 2\n", "76.68: 1\n", "150.94: 1\n", "78.03: 1\n", "77.42: 1\n", "302.4: 1\n", "180.42: 1\n", "277.88: 1\n", "543.28: 1\n", "204.37: 1\n", "298.8: 1\n", "283.12: 1\n", "47.65: 2\n", "128.8: 1\n", "77.5: 1\n", "52.66: 1\n", "212.33: 1\n", "400.36: 1\n", "585.94: 1\n", "14.7: 1\n", "37.65: 1\n", "67.14: 1\n", "328.57: 1\n", "384.75: 1\n", "178.71: 1\n", "349.87: 1\n", "381.38: 1\n", "105.32: 1\n", "405.92: 1\n", "62.94: 2\n", "80.94: 1\n", "79.58: 2\n", "45.16: 1\n", "166.12: 1\n", "68.24: 1\n", "331.48: 1\n", "161.72: 1\n", "26.27: 1\n", "54.32: 1\n", "241.79: 1\n", "135.1: 1\n", "273.76: 1\n", "157.99: 1\n", "469.14: 1\n", "82.91: 1\n", "130.42: 1\n", "141.61: 1\n", "336.68: 1\n", "229.18: 1\n", "64.28: 1\n", "272.29: 1\n", "109.15: 1\n", "135.05: 2\n", "16.06: 1\n", "51.9: 1\n", "229.4: 1\n", "108.49: 1\n", "28.01: 1\n", "85.8: 1\n", "61.77: 2\n", "278.67: 2\n", "194.59: 1\n", "197.4: 1\n", "183.17: 1\n", "395.39: 1\n", "316.39: 1\n", "109.66: 1\n", "121.71: 1\n", "224.76: 2\n", "284.66: 1\n", "10.83: 1\n", "47.01: 1\n", "213.98: 1\n", "36.07: 1\n", "167.72: 1\n", "47.48: 1\n", "21.63: 1\n", "328.79: 1\n", "115.34: 2\n", "12.36: 1\n", "81.27: 1\n", "259.7: 2\n", "57.08: 1\n", "569.75: 1\n", "219.75: 1\n", "127.88: 1\n", "129.84: 1\n", "77.63: 1\n", "11.28: 1\n", "5.28: 1\n", "68.8: 1\n", "197.95: 1\n", "34.02: 1\n", "213.68: 1\n", "41.55: 2\n", "165.24: 1\n", "54.14: 1\n", "281.03: 1\n", "82.64: 1\n", "70.67: 1\n", "629.7: 1\n", "177.6: 1\n", "29.06: 1\n", "382.39: 1\n", "173.86: 1\n", "21.22: 1\n", "23.35: 1\n", "461.22: 1\n", "401.69: 1\n", "128.39: 1\n", "24.57: 1\n", "290.83: 1\n", "213.85: 1\n", "69.69: 2\n", "175.79: 1\n", "143.55: 1\n", "329.58: 1\n", "323.45: 1\n", "187.87: 1\n", "27.61: 1\n", "29.95: 1\n", "152.97: 1\n", "19.41: 1\n", "70.49: 1\n", "199.74: 1\n", "420.46: 1\n", "60.68: 1\n", "152.35: 3\n", "137.28: 1\n", "139.84: 1\n", "78.43: 1\n", "135.83: 1\n", "183.67: 1\n", "46.41: 1\n", "229.78: 1\n", "77.06: 1\n", "111.44: 1\n", "36.5: 1\n", "85.98: 2\n", "31.81: 1\n", "40.11: 2\n", "95.7: 2\n", "40.49: 2\n", "131.98: 1\n", "46.91: 1\n", "24.2: 1\n", "84.79: 2\n", "120.9: 1\n", "158.8: 1\n", "159.62: 1\n", "48.97: 2\n", "357.81: 1\n", "266.87: 1\n", "51.17: 1\n", "121.22: 2\n", "38.6: 1\n", "226.65: 1\n", "129.2: 2\n", "40.15: 1\n", "82.97: 1\n", "235.46: 1\n", "188.21: 1\n", "290.29: 1\n", "207.66: 1\n", "54.31: 1\n", "244.56: 1\n", "264.9: 1\n", "38.54: 1\n", "167.85: 1\n", "120.2: 1\n", "333.56: 1\n", "127.28: 1\n", "61.45: 3\n", "215.73: 1\n", "184.81: 1\n", "55.84: 2\n", "81.7: 1\n", "75.87: 1\n", "33.13: 1\n", "120.22: 1\n", "130.91: 1\n", "73.05: 2\n", "14.26: 1\n", "48.66: 2\n", "24.72: 1\n", "85.41: 2\n", "115.84: 1\n", "124.18: 1\n", "93.39: 2\n", "29.76: 1\n", "153.46: 1\n", "388.77: 1\n", "31.47: 1\n", "385.18: 1\n", "145.97: 2\n", "220.28: 1\n", "93.14: 1\n", "147.13: 1\n", "294.94: 1\n", "135.69: 1\n", "106.84: 1\n", "19.14: 1\n", "138.31: 1\n", "91.29: 1\n", "146.38: 1\n", "58.01: 1\n", "117.82: 1\n", "243.62: 2\n", "43.89: 1\n", "60.79: 1\n", "213.29: 1\n", "116.25: 3\n", "25.53: 2\n", "21.39: 1\n", "42.9: 1\n", "109.47: 1\n", "286.79: 1\n", "131.36: 2\n", "180.29: 1\n", "43.71: 2\n", "118.13: 2\n", "9.93: 2\n", "333.62: 1\n", "30.14: 2\n", "227.45: 1\n", "106.49: 1\n", "198.49: 1\n", "20.69: 1\n", "134.65: 2\n", "104.63: 2\n", "167.73: 1\n", "40.83: 1\n", "366.27: 1\n", "14.66: 1\n", "311.46: 1\n", "131.71: 1\n", "26.85: 2\n", "80.44: 1\n", "40.13: 1\n", "140.14: 1\n", "230.05: 1\n", "52.03: 1\n", "321.64: 1\n", "105.83: 1\n", "319.29: 1\n", "59.33: 1\n", "80.79: 1\n", "237.16: 1\n", "40.4: 1\n", "221.14: 1\n", "201.3: 1\n", "231.05: 1\n", "10.62: 1\n", "2.02: 1\n", "68.81: 2\n", "260.3: 1\n", "24.56: 3\n", "252.66: 1\n", "22.66: 1\n", "54.73: 2\n", "45.27: 2\n", "249.13: 1\n", "487.2: 1\n", "106.98: 1\n", "61.93: 2\n", "124.59: 1\n", "56.84: 1\n", "230.17: 1\n", "60.39: 1\n", "58.88: 2\n", "263.43: 1\n", "241.35: 1\n", "19.8: 1\n", "146.83: 1\n", "68.94: 1\n", "33.87: 1\n", "293.94: 1\n", "48.14: 1\n", "65.57: 1\n", "102.64: 1\n", "330.58: 1\n", "242.36: 1\n", "140.51: 1\n", "155.65: 1\n", "13.98: 1\n", "227.72: 1\n", "295.69: 1\n", "507.43: 1\n", "146.81: 2\n", "347.0: 1\n", "182.98: 1\n", "49.98: 3\n", "148.8: 1\n", "191.4: 1\n", "164.28: 1\n", "199.68: 3\n", "55.05: 2\n", "114.65: 1\n", "14.41: 1\n", "287.66: 1\n", "156.08: 2\n", "40.97: 1\n", "139.87: 1\n", "66.72: 1\n", "195.01: 1\n", "161.66: 1\n", "210.01: 1\n", "66.41: 1\n", "131.46: 1\n", "203.09: 1\n", "100.51: 2\n", "64.4: 2\n", "24.43: 1\n", "133.41: 1\n", "179.68: 1\n", "70.34: 1\n", "22.08: 2\n", "384.82: 1\n", "91.55: 1\n", "200.57: 1\n", "92.63: 1\n", "168.32: 1\n", "60.06: 2\n", "101.99: 2\n", "79.49: 1\n", "36.38: 1\n", "17.88: 1\n", "167.94: 1\n", "6.38: 1\n", "233.95: 1\n", "64.37: 2\n", "161.74: 1\n", "70.7: 1\n", "93.21: 1\n", "42.93: 1\n", "99.87: 1\n", "68.23: 1\n", "52.73: 1\n", "315.32: 1\n", "410.98: 1\n", "47.07: 2\n", "75.56: 1\n", "160.59: 2\n", "396.34: 2\n", "127.7: 1\n", "11.19: 1\n", "72.89: 1\n", "57.42: 1\n", "227.6: 1\n", "0.64: 1\n", "251.41: 1\n", "123.72: 2\n", "63.22: 2\n", "470.01: 1\n", "54.53: 1\n", "64.3: 2\n", "364.99: 1\n", "158.37: 1\n", "72.98: 1\n", "154.72: 1\n", "247.65: 1\n", "136.18: 1\n", "76.56: 2\n", "213.58: 1\n", "271.9: 2\n", "388.27: 1\n", "162.95: 1\n", "489.79: 1\n", "8.4: 1\n", "169.4: 1\n", "24.27: 1\n", "60.31: 1\n", "168.94: 1\n", "176.57: 1\n", "186.84: 1\n", "109.31: 1\n", "168.62: 1\n", "29.33: 1\n", "123.27: 1\n", "41.27: 1\n", "77.48: 2\n", "61.46: 1\n", "42.67: 2\n", "287.04: 1\n", "33.49: 2\n", "21.99: 2\n", "17.91: 1\n", "48.55: 3\n", "68.42: 1\n", "34.95: 1\n", "69.83: 1\n", "88.61: 1\n", "31.9: 1\n", "34.86: 1\n", "25.1: 2\n", "34.49: 1\n", "282.46: 1\n", "19.53: 1\n", "271.78: 1\n", "165.53: 1\n", "163.31: 1\n", "203.63: 1\n", "46.39: 1\n", "59.88: 1\n", "12.55: 1\n", "184.69: 1\n", "30.08: 1\n", "269.38: 1\n", "130.88: 1\n", "185.55: 1\n", "154.52: 1\n", "64.72: 1\n", "81.91: 1\n", "21.72: 1\n", "145.42: 1\n", "40.09: 1\n", "11.93: 1\n", "70.97: 1\n", "44.83: 1\n", "53.81: 1\n", "16.1: 1\n", "272.38: 1\n", "240.41: 1\n", "266.53: 1\n", "41.87: 1\n", "169.92: 2\n", "97.7: 1\n", "312.32: 1\n", "295.93: 1\n", "272.85: 1\n", "68.93: 1\n", "562.34: 1\n", "153.3: 1\n", "126.88: 1\n", "22.22: 2\n", "130.67: 1\n", "274.79: 1\n", "363.37: 1\n", "133.51: 1\n", "315.29: 1\n", "98.78: 1\n", "101.58: 1\n", "314.54: 1\n", "90.98: 1\n", "275.43: 1\n", "72.1: 1\n", "43.18: 2\n", "289.88: 1\n", "66.95: 2\n", "51.06: 1\n", "56.31: 2\n", "107.43: 2\n", "114.17: 1\n", "112.6: 1\n", "15.45: 1\n", "174.92: 1\n", "310.07: 1\n", "71.95: 1\n", "147.65: 2\n", "79.02: 1\n", "153.76: 1\n", "101.19: 1\n", "15.04: 1\n", "172.89: 2\n", "204.02: 1\n", "17.93: 2\n", "240.52: 1\n", "122.02: 1\n", "219.63: 1\n", "270.96: 1\n", "17.74: 1\n", "172.49: 1\n", "96.26: 2\n", "89.85: 2\n", "76.91: 2\n", "301.96: 1\n", "216.38: 1\n", "77.96: 2\n", "237.12: 1\n", "112.48: 2\n", "228.34: 1\n", "50.84: 1\n", "243.3: 1\n", "24.32: 1\n", "182.47: 1\n", "77.57: 1\n", "134.47: 2\n", "117.53: 1\n", "180.36: 1\n", "81.18: 1\n", "22.57: 1\n", "65.96: 1\n", "148.51: 1\n", "154.87: 1\n", "251.02: 1\n", "50.81: 1\n", "56.56: 1\n", "147.67: 1\n", "42.11: 1\n", "150.83: 1\n", "272.27: 1\n", "38.7: 2\n", "482.99: 1\n", "35.36: 2\n", "87.38: 1\n", "81.52: 1\n", "84.55: 3\n", "166.21: 1\n", "96.02: 1\n", "104.45: 2\n", "509.63: 1\n", "277.04: 1\n", "65.79: 1\n", "8.0: 1\n", "8.58: 1\n", "177.15: 2\n", "118.88: 1\n", "24.14: 1\n", "100.77: 1\n", "97.84: 2\n", "17.48: 2\n", "94.2: 1\n", "152.58: 1\n", "150.79: 1\n", "17.97: 1\n", "97.16: 1\n", "45.19: 1\n", "156.12: 1\n", "42.08: 1\n", "208.48: 2\n", "38.5: 1\n", "73.26: 2\n", "94.84: 2\n", "49.85: 1\n", "7.18: 1\n", "2.15: 1\n", "45.28: 1\n", "167.14: 1\n", "101.42: 1\n", "81.84: 1\n", "10.23: 1\n", "72.4: 1\n", "9.84: 1\n", "48.11: 1\n", "148.92: 2\n", "131.75: 2\n", "202.88: 1\n", "58.97: 1\n", "96.07: 1\n", "212.58: 2\n", "12.53: 1\n", "39.4: 1\n", "75.46: 1\n", "148.19: 1\n", "43.94: 1\n", "188.27: 1\n", "149.24: 1\n", "38.92: 2\n", "111.48: 2\n", "99.93: 1\n", "137.15: 1\n", "46.7: 1\n", "192.63: 1\n", "81.96: 2\n", "139.32: 1\n", "35.15: 1\n", "160.06: 1\n", "83.35: 1\n", "638.13: 1\n", "66.3: 1\n", "41.84: 1\n", "96.87: 1\n", "52.91: 1\n", "236.87: 1\n", "167.59: 1\n", "78.86: 1\n", "10.52: 1\n", "161.54: 1\n", "174.02: 1\n", "149.0: 1\n", "400.49: 1\n", "103.31: 1\n", "130.13: 1\n", "184.06: 1\n", "184.92: 1\n", "225.99: 1\n", "175.0: 1\n", "113.01: 1\n", "17.4: 2\n", "107.24: 1\n", "73.22: 2\n", "172.59: 1\n", "52.98: 1\n", "125.72: 1\n", "397.62: 2\n", "180.6: 1\n", "28.27: 1\n", "291.75: 1\n", "140.89: 1\n", "211.78: 1\n", "207.53: 1\n", "105.97: 2\n", "58.77: 1\n", "131.56: 2\n", "27.2: 2\n", "184.08: 1\n", "9.2: 1\n", "32.2: 3\n", "62.19: 1\n", "58.84: 1\n", "118.12: 1\n", "335.08: 1\n", "145.15: 1\n", "31.66: 2\n", "71.82: 1\n", "277.69: 1\n", "97.75: 2\n", "120.14: 1\n", "163.55: 1\n", "15.6: 2\n", "302.19: 1\n", "50.23: 1\n", "108.17: 1\n", "282.21: 1\n", "21.78: 1\n", "119.37: 1\n", "53.14: 2\n", "56.89: 1\n", "268.33: 1\n", "81.24: 1\n", "34.21: 1\n", "169.27: 1\n", "122.0: 2\n", "228.92: 1\n", "390.32: 1\n", "3.82: 1\n", "49.72: 1\n", "26.04: 1\n", "124.35: 1\n", "18.23: 2\n", "162.61: 1\n", "69.31: 1\n", "86.53: 1\n", "109.1: 3\n", "141.31: 1\n", "74.21: 2\n", "261.06: 1\n", "167.68: 1\n", "71.78: 1\n", "178.96: 1\n", "191.52: 1\n", "34.5: 1\n", "191.53: 1\n", "151.61: 1\n", "225.61: 1\n", "32.92: 1\n", "139.81: 1\n", "46.76: 1\n", "54.34: 1\n", "129.72: 1\n", "17.59: 1\n", "45.66: 2\n", "112.93: 1\n", "187.86: 1\n", "21.71: 1\n", "188.6: 2\n", "420.62: 1\n", "99.79: 1\n", "31.14: 1\n", "50.2: 3\n", "20.38: 1\n", "249.01: 1\n", "152.62: 1\n", "299.61: 1\n", "82.81: 1\n", "16.04: 1\n", "24.07: 2\n", "145.62: 2\n", "35.94: 1\n", "161.26: 1\n", "218.02: 1\n", "284.92: 1\n", "178.85: 1\n", "121.14: 1\n", "12.45: 1\n", "408.96: 1\n", "142.83: 1\n", "141.84: 1\n", "32.31: 1\n", "192.37: 1\n", "165.78: 1\n", "19.42: 1\n", "24.1: 1\n", "27.06: 1\n", "12.52: 1\n", "27.31: 1\n", "88.46: 1\n", "75.71: 1\n", "156.99: 3\n", "34.69: 1\n", "96.94: 1\n", "38.13: 1\n", "187.14: 1\n", "278.39: 1\n", "217.61: 1\n", "133.03: 2\n", "20.95: 1\n", "248.18: 1\n", "85.13: 1\n", "60.32: 2\n", "133.65: 1\n", "233.8: 1\n", "381.74: 1\n", "8.47: 1\n", "257.6: 2\n", "46.18: 1\n", "189.93: 1\n", "173.26: 2\n", "196.4: 1\n", "146.96: 1\n", "45.54: 1\n", "246.76: 1\n", "36.55: 2\n", "377.73: 1\n", "41.15: 1\n", "31.87: 1\n", "187.88: 1\n", "220.88: 1\n", "206.46: 1\n", "41.14: 2\n", "25.8: 1\n", "155.09: 1\n", "160.24: 1\n", "167.07: 1\n", "19.77: 1\n", "26.88: 1\n", "351.52: 1\n", "22.81: 1\n", "76.22: 1\n", "142.57: 1\n", "11.03: 2\n", "128.17: 1\n", "303.65: 1\n", "86.9: 1\n", "176.45: 1\n", "48.35: 1\n", "199.45: 1\n", "25.97: 1\n", "222.21: 1\n", "138.65: 1\n", "263.82: 1\n", "306.36: 1\n", "186.37: 1\n", "128.22: 1\n", "140.5: 2\n", "23.23: 2\n", "146.72: 1\n", "50.34: 3\n", "85.75: 1\n", "45.97: 2\n", "153.82: 1\n", "45.06: 4\n", "121.51: 1\n", "49.45: 1\n", "34.05: 1\n", "32.33: 1\n", "14.3: 1\n", "221.82: 1\n", "219.31: 1\n", "161.49: 1\n", "27.18: 3\n", "89.17: 1\n", "166.1: 1\n", "93.56: 1\n", "357.76: 1\n", "127.41: 1\n", "129.16: 1\n", "153.7: 2\n", "81.4: 1\n", "390.02: 1\n", "152.03: 1\n", "62.38: 2\n", "85.83: 1\n", "153.91: 1\n", "114.57: 1\n", "95.0: 1\n", "102.85: 1\n", "237.94: 1\n", "65.33: 1\n", "305.13: 1\n", "47.33: 1\n", "66.33: 2\n", "153.95: 2\n", "71.24: 1\n", "280.89: 1\n", "306.63: 1\n", "177.57: 1\n", "116.86: 1\n", "26.64: 1\n", "14.4: 1\n", "294.56: 1\n", "35.77: 1\n", "92.56: 1\n", "64.34: 1\n", "214.33: 1\n", "463.7: 1\n", "20.43: 1\n", "37.76: 2\n", "82.06: 1\n", "172.71: 1\n", "128.85: 1\n", "116.7: 1\n", "209.3: 1\n", "93.6: 1\n", "319.8: 1\n", "163.7: 2\n", "499.02: 1\n", "32.72: 1\n", "94.98: 1\n", "256.82: 1\n", "41.34: 2\n", "257.72: 1\n", "113.7: 1\n", "140.36: 1\n", "19.27: 2\n", "104.71: 1\n", "38.68: 1\n", "186.29: 1\n", "243.63: 1\n", "145.52: 2\n", "415.81: 1\n", "16.97: 2\n", "78.58: 2\n", "58.5: 2\n", "30.18: 1\n", "165.34: 1\n", "130.82: 1\n", "107.37: 1\n", "172.8: 1\n", "236.44: 2\n", "50.63: 1\n", "55.42: 1\n", "94.3: 1\n", "34.81: 1\n", "210.55: 1\n", "4.32: 1\n", "31.58: 1\n", "41.81: 1\n", "218.31: 1\n", "120.41: 1\n", "174.11: 1\n", "20.14: 1\n", "37.59: 1\n", "120.55: 2\n", "267.29: 1\n", "11.91: 1\n", "220.04: 1\n", "16.16: 1\n", "290.07: 1\n", "91.06: 1\n", "262.66: 1\n", "19.38: 1\n", "42.1: 1\n", "135.98: 2\n", "263.42: 1\n", "132.2: 1\n", "4.67: 1\n", "64.46: 1\n", "51.73: 1\n", "76.17: 1\n", "55.67: 1\n", "7.31: 1\n", "94.8: 1\n", "167.31: 1\n", "45.17: 1\n", "322.3: 1\n", "191.91: 2\n", "87.63: 1\n", "79.41: 1\n", "169.09: 1\n", "68.44: 2\n", "83.23: 1\n", "72.91: 2\n", "39.74: 1\n", "37.66: 3\n", "488.8: 1\n", "273.98: 1\n", "11.75: 1\n", "93.52: 1\n", "240.05: 1\n", "206.7: 1\n", "18.44: 1\n", "21.07: 1\n", "184.89: 1\n", "23.38: 1\n", "60.41: 2\n", "157.32: 1\n", "118.58: 1\n", "40.39: 2\n", "176.19: 1\n", "223.97: 1\n", "154.18: 1\n", "366.31: 1\n", "50.35: 1\n", "17.89: 2\n", "43.45: 1\n", "114.72: 1\n", "37.6: 1\n", "98.34: 1\n", "87.43: 1\n", "186.76: 2\n", "413.63: 1\n", "66.27: 1\n", "139.2: 1\n", "167.1: 1\n", "19.0: 1\n", "428.3: 1\n", "181.24: 1\n", "16.33: 3\n", "216.52: 3\n", "170.84: 2\n", "469.02: 1\n", "86.97: 2\n", "110.88: 2\n", "140.29: 1\n", "41.16: 2\n", "130.79: 1\n", "124.61: 3\n", "171.86: 1\n", "235.04: 1\n", "256.62: 1\n", "33.94: 1\n", "49.18: 1\n", "198.41: 1\n", "329.91: 1\n", "73.33: 1\n", "393.42: 1\n", "26.53: 1\n", "56.59: 1\n", "25.63: 2\n", "197.76: 1\n", "25.48: 1\n", "210.99: 1\n", "222.92: 1\n", "10.86: 1\n", "62.42: 1\n", "87.74: 2\n", "34.1: 1\n", "39.17: 1\n", "72.51: 1\n", "11.49: 1\n", "107.25: 2\n", "278.08: 1\n", "434.6: 1\n", "161.51: 2\n", "41.24: 1\n", "82.09: 1\n", "183.64: 1\n", "53.7: 2\n", "19.65: 2\n", "97.86: 1\n", "152.95: 1\n", "326.46: 1\n", "229.89: 1\n", "204.85: 1\n", "47.72: 1\n", "34.74: 1\n", "268.04: 1\n", "240.65: 1\n", "83.71: 1\n", "27.45: 1\n", "66.99: 1\n", "106.09: 1\n", "122.23: 1\n", "14.37: 2\n", "50.17: 1\n", "19.19: 1\n", "382.36: 1\n", "179.35: 1\n", "61.73: 1\n", "243.97: 1\n", "93.71: 1\n", "131.05: 1\n", "30.56: 1\n", "57.58: 1\n", "111.77: 1\n", "449.0: 1\n", "54.71: 1\n", "288.66: 1\n", "274.93: 1\n", "106.77: 1\n", "53.58: 1\n", "48.19: 1\n", "312.22: 1\n", "257.39: 1\n", "65.92: 1\n", "233.02: 1\n", "36.39: 1\n", "40.27: 1\n", "239.01: 1\n", "412.73: 1\n", "24.76: 1\n", "40.54: 2\n", "9.14: 1\n", "231.29: 1\n", "357.07: 1\n", "184.79: 1\n", "23.67: 1\n", "84.96: 1\n", "350.32: 1\n", "124.48: 1\n", "165.52: 1\n", "148.46: 1\n", "33.5: 1\n", "134.56: 1\n", "114.3: 1\n", "26.09: 1\n", "55.26: 2\n", "22.3: 1\n", "146.8: 1\n", "36.06: 1\n", "236.9: 1\n", "85.92: 1\n", "16.05: 1\n", "164.21: 1\n", "267.35: 2\n", "293.1: 1\n", "154.06: 1\n", "65.65: 1\n", "19.49: 1\n", "50.68: 1\n", "357.3: 1\n", "278.7: 1\n", "44.06: 1\n", "231.45: 1\n", "123.78: 1\n", "85.21: 1\n", "34.54: 1\n", "265.9: 2\n", "135.97: 1\n", "22.12: 1\n", "137.51: 1\n", "244.31: 1\n", "481.88: 1\n", "82.46: 1\n", "83.14: 1\n", "164.8: 2\n", "15.69: 1\n", "209.04: 1\n", "120.48: 1\n", "37.73: 1\n", "188.97: 1\n", "42.34: 1\n", "109.62: 1\n", "119.97: 1\n", "366.74: 1\n", "77.02: 1\n", "352.36: 1\n", "136.09: 2\n", "115.65: 1\n", "68.04: 1\n", "311.37: 1\n", "10.51: 1\n", "261.95: 1\n", "126.13: 1\n", "271.06: 1\n", "126.7: 1\n", "50.65: 2\n", "147.34: 1\n", "54.83: 1\n", "144.19: 1\n", "116.39: 1\n", "235.59: 1\n", "39.84: 1\n", "223.38: 1\n", "239.21: 1\n", "211.89: 1\n", "190.71: 1\n", "18.31: 2\n", "57.17: 2\n", "158.17: 1\n", "3.99: 1\n", "33.02: 3\n", "17.75: 1\n", "140.71: 1\n", "42.83: 1\n", "314.69: 1\n", "380.47: 1\n", "132.71: 1\n", "123.66: 2\n", "218.06: 2\n", "62.72: 1\n", "39.56: 1\n", "136.69: 1\n", "122.42: 2\n", "44.23: 1\n", "346.46: 1\n", "68.72: 2\n", "62.17: 1\n", "213.14: 1\n", "60.66: 1\n", "28.71: 2\n", "112.27: 1\n", "36.53: 1\n", "198.51: 2\n", "28.12: 1\n", "173.05: 1\n", "56.44: 1\n", "15.26: 1\n", "346.72: 1\n", "71.74: 1\n", "48.33: 1\n", "58.95: 1\n", "107.34: 1\n", "15.07: 1\n", "190.83: 1\n", "52.43: 1\n", "58.9: 1\n", "28.06: 1\n", "200.6: 2\n", "62.21: 1\n", "179.44: 1\n", "272.43: 1\n", "212.98: 1\n", "32.97: 2\n", "37.33: 1\n", "199.08: 1\n", "60.11: 1\n", "28.4: 1\n", "14.52: 1\n", "290.17: 1\n", "125.32: 1\n", "213.39: 2\n", "53.26: 1\n", "154.51: 2\n", "151.46: 1\n", "15.15: 1\n", "13.9: 1\n", "20.85: 1\n", "111.31: 1\n", "100.61: 1\n", "98.8: 1\n", "83.81: 1\n", "146.48: 1\n", "22.75: 1\n", "56.71: 1\n", "55.23: 3\n", "52.71: 1\n", "119.51: 2\n", "116.17: 1\n", "213.52: 1\n", "191.08: 1\n", "130.53: 1\n", "51.72: 1\n", "42.86: 1\n", "143.0: 1\n", "353.92: 1\n", "286.21: 2\n", "223.65: 1\n", "112.3: 1\n", "213.89: 1\n", "9.55: 1\n", "43.86: 2\n", "28.79: 1\n", "25.61: 1\n", "134.12: 1\n", "88.12: 1\n", "358.78: 1\n", "32.27: 1\n", "60.6: 1\n", "308.34: 1\n", "48.74: 1\n", "420.98: 1\n", "107.47: 1\n", "173.45: 1\n", "282.15: 1\n", "82.73: 1\n", "20.49: 2\n", "241.22: 1\n", "126.99: 1\n", "8.62: 1\n", "25.79: 1\n", "481.96: 1\n", "97.14: 2\n", "282.42: 1\n", "86.19: 1\n", "295.35: 1\n", "119.9: 1\n", "127.1: 1\n", "170.85: 1\n", "58.16: 2\n", "45.89: 1\n", "36.65: 2\n", "18.09: 1\n", "113.61: 2\n", "98.63: 2\n", "23.41: 1\n", "123.0: 1\n", "255.33: 1\n", "69.53: 1\n", "72.61: 1\n", "124.43: 1\n", "15.92: 1\n", "44.88: 1\n", "10.61: 1\n", "69.67: 1\n", "223.0: 2\n", "137.27: 1\n", "204.22: 1\n", "41.51: 1\n", "167.95: 1\n", "106.32: 1\n", "70.61: 1\n", "45.83: 1\n", "481.9: 1\n", "211.05: 1\n", "108.42: 1\n", "98.09: 1\n", "113.66: 1\n", "254.14: 1\n", "26.49: 1\n", "24.09: 1\n", "79.32: 1\n", "326.06: 1\n", "278.48: 1\n", "405.2: 1\n", "60.49: 1\n", "136.65: 1\n", "149.25: 1\n", "89.52: 2\n", "122.18: 1\n", "238.16: 1\n", "249.18: 1\n", "82.33: 2\n", "22.27: 1\n", "78.09: 1\n", "15.12: 1\n", "25.85: 1\n", "68.13: 1\n", "204.28: 1\n", "167.66: 1\n", "281.89: 1\n", "235.11: 1\n", "40.96: 1\n", "34.52: 1\n", "18.43: 1\n", "112.68: 1\n", "47.37: 1\n", "197.38: 1\n", "140.65: 1\n", "6.3: 1\n", "190.23: 1\n", "110.03: 1\n", "54.47: 1\n", "16.84: 1\n", "39.27: 1\n", "269.17: 1\n", "39.12: 1\n", "107.9: 1\n", "101.91: 1\n", "166.41: 1\n", "158.65: 2\n", "201.16: 1\n", "17.78: 1\n", "38.36: 2\n", "18.19: 1\n", "25.99: 1\n", "37.93: 1\n", "34.33: 1\n", "216.05: 1\n", "33.17: 1\n", "229.99: 1\n", "189.43: 1\n", "67.85: 1\n", "119.65: 1\n", "260.73: 1\n", "51.71: 1\n", "162.24: 1\n", "151.18: 1\n", "73.21: 1\n", "97.44: 1\n", "34.36: 1\n", "14.18: 1\n", "297.79: 1\n", "39.24: 1\n", "58.38: 2\n", "136.39: 1\n", "135.66: 1\n", "141.91: 1\n", "92.71: 1\n", "31.02: 1\n", "98.07: 1\n", "232.73: 1\n", "102.9: 2\n", "85.45: 1\n", "215.5: 1\n", "209.18: 1\n", "91.49: 1\n", "39.67: 1\n", "135.89: 1\n", "186.6: 1\n", "25.34: 1\n", "144.49: 1\n", "16.89: 1\n", "323.87: 1\n", "146.97: 1\n", "263.67: 1\n", "85.73: 2\n", "35.85: 3\n", "113.15: 1\n", "49.91: 2\n", "211.56: 1\n", "17.69: 1\n", "135.39: 1\n", "41.43: 1\n", "180.25: 1\n", "94.6: 1\n", "228.83: 1\n", "207.01: 1\n", "116.37: 1\n", "143.32: 1\n", "162.55: 1\n", "171.46: 1\n", "25.7: 2\n", "305.18: 1\n", "111.86: 1\n", "27.0: 1\n", "43.11: 1\n", "94.88: 1\n", "259.28: 1\n", "214.64: 1\n", "197.14: 1\n", "28.14: 1\n", "287.06: 1\n", "11.94: 2\n", "235.01: 1\n", "80.18: 1\n", "150.81: 1\n", "36.29: 1\n", "58.45: 2\n", "176.78: 1\n", "132.27: 1\n", "208.91: 2\n", "47.11: 1\n", "47.3: 1\n", "25.25: 1\n", "150.37: 1\n", "28.69: 1\n", "86.94: 1\n", "23.83: 1\n", "116.18: 2\n", "57.41: 1\n", "100.76: 1\n", "50.77: 2\n", "155.01: 1\n", "60.28: 1\n", "260.7: 1\n", "7.27: 1\n", "16.51: 1\n", "100.98: 1\n", "81.8: 1\n", "197.49: 1\n", "56.22: 1\n", "84.74: 2\n", "23.66: 2\n", "293.36: 1\n", "150.32: 1\n", "278.72: 2\n", "19.34: 1\n", "145.38: 1\n", "96.3: 2\n", "313.47: 1\n", "235.18: 1\n", "20.29: 1\n", "66.46: 1\n", "90.99: 2\n", "313.27: 1\n", "135.73: 1\n", "88.72: 1\n", "33.07: 1\n", "177.4: 1\n", "282.8: 1\n", "37.8: 1\n", "70.26: 2\n", "38.42: 2\n", "10.88: 2\n", "209.12: 1\n", "11.57: 1\n", "167.65: 1\n", "54.74: 1\n", "31.12: 1\n", "18.84: 3\n", "16.78: 1\n", "82.37: 3\n", "211.76: 1\n", "45.45: 1\n", "225.65: 1\n", "12.84: 1\n", "89.51: 1\n", "143.58: 1\n", "381.95: 1\n", "151.11: 1\n", "159.74: 2\n", "216.12: 1\n", "27.58: 1\n", "163.99: 1\n", "33.44: 1\n", "6.53: 1\n", "29.12: 2\n", "91.31: 1\n", "5.63: 1\n", "171.73: 1\n", "47.21: 1\n", "23.94: 1\n", "103.66: 2\n", "228.45: 1\n", "68.12: 1\n", "19.11: 1\n", "117.93: 1\n", "58.7: 1\n", "259.13: 1\n", "126.5: 2\n", "55.18: 2\n", "364.95: 1\n", "162.26: 1\n", "224.39: 1\n", "11.33: 1\n", "181.51: 1\n", "122.97: 1\n", "123.47: 1\n", "112.22: 1\n", "21.08: 1\n", "128.2: 1\n", "14.32: 1\n", "42.03: 1\n", "327.96: 1\n", "73.2: 1\n", "32.48: 1\n", "300.48: 1\n", "103.35: 1\n", "39.25: 1\n", "14.03: 1\n", "18.11: 1\n", "28.29: 1\n", "188.55: 1\n", "28.88: 1\n", "31.3: 1\n", "121.5: 1\n", "22.18: 1\n", "32.32: 1\n", "26.68: 1\n", "95.22: 1\n", "155.49: 1\n", "205.06: 2\n", "204.63: 1\n", "117.51: 1\n", "66.79: 4\n", "47.82: 1\n", "365.48: 1\n", "254.69: 1\n", "180.13: 1\n", "129.45: 1\n", "57.68: 1\n", "21.83: 1\n", "94.29: 1\n", "93.95: 4\n", "221.27: 1\n", "220.91: 1\n", "76.31: 1\n", "66.49: 1\n", "123.6: 1\n", "19.08: 1\n", "158.81: 1\n", "24.64: 1\n", "163.13: 1\n", "147.07: 1\n", "102.47: 1\n", "102.89: 1\n", "364.91: 1\n", "77.56: 2\n", "240.96: 1\n", "87.71: 2\n", "31.63: 1\n", "39.7: 1\n", "199.41: 2\n", "357.78: 1\n", "39.79: 1\n", "53.36: 1\n", "35.55: 1\n", "81.46: 1\n", "82.8: 1\n", "188.38: 1\n", "161.76: 1\n", "74.58: 1\n", "122.17: 2\n", "134.88: 1\n", "122.43: 1\n", "335.49: 1\n", "71.43: 1\n", "65.88: 1\n", "59.43: 1\n", "305.94: 1\n", "109.98: 2\n", "118.79: 2\n", "96.15: 1\n", "27.03: 1\n", "351.23: 1\n", "148.29: 1\n", "37.22: 1\n", "28.05: 1\n", "294.32: 1\n", "37.89: 1\n", "134.6: 1\n", "212.16: 1\n", "292.61: 1\n", "221.96: 1\n", "57.84: 1\n", "235.05: 1\n", "18.74: 1\n", "27.47: 1\n", "16.27: 1\n", "161.99: 1\n", "13.89: 1\n", "103.84: 1\n", "121.81: 1\n", "100.03: 1\n", "150.26: 1\n", "84.02: 1\n", "154.35: 1\n", "8.92: 1\n", "216.4: 1\n", "30.04: 1\n", "89.63: 2\n", "218.1: 1\n", "71.09: 1\n", "238.86: 1\n", "509.24: 1\n", "172.72: 2\n", "42.72: 1\n", "195.46: 1\n", "60.03: 1\n", "132.32: 1\n", "140.26: 1\n", "162.17: 1\n", "48.5: 1\n", "135.59: 1\n", "158.6: 1\n", "23.07: 1\n", "103.51: 2\n", "261.93: 1\n", "221.3: 1\n", "71.08: 1\n", "114.23: 1\n", "110.01: 1\n", "58.15: 1\n", "114.87: 1\n", "19.89: 1\n", "232.83: 1\n", "122.95: 1\n", "80.36: 1\n", "94.35: 1\n", "55.1: 2\n", "21.82: 1\n", "99.88: 1\n", "23.15: 1\n", "175.5: 1\n", "84.27: 1\n", "139.27: 2\n", "143.24: 1\n", "43.66: 1\n", "142.64: 1\n", "217.42: 2\n", "38.57: 1\n", "149.49: 2\n", "230.51: 1\n", "61.35: 1\n", "352.72: 1\n", "260.77: 1\n", "20.46: 1\n", "15.71: 1\n", "5.05: 1\n", "78.96: 1\n", "124.64: 1\n", "169.43: 1\n", "33.33: 1\n", "76.69: 1\n", "62.2: 1\n", "93.17: 1\n", "271.64: 1\n", "83.38: 1\n", "278.91: 1\n", "22.95: 1\n", "18.93: 1\n", "210.46: 1\n", "81.37: 1\n", "144.18: 1\n", "106.16: 1\n", "132.02: 1\n", "29.11: 1\n", "9.12: 2\n", "122.6: 1\n", "34.8: 1\n", "61.9: 2\n", "334.4: 1\n", "23.37: 1\n", "152.1: 1\n", "164.89: 1\n", "26.93: 1\n", "17.63: 1\n", "26.2: 1\n", "163.82: 2\n", "170.8: 1\n", "214.59: 1\n", "78.24: 1\n", "310.28: 1\n", "13.2: 1\n", "42.23: 1\n", "75.85: 1\n", "150.97: 1\n", "18.97: 1\n", "116.62: 1\n", "88.23: 1\n", "32.06: 1\n", "267.94: 1\n", "209.23: 1\n", "122.77: 1\n", "129.43: 1\n", "81.15: 1\n", "10.43: 1\n", "194.93: 1\n", "80.43: 1\n", "109.56: 1\n", "30.45: 2\n", "142.89: 2\n", "306.27: 1\n", "7.48: 1\n", "235.49: 1\n", "42.43: 1\n", "68.97: 1\n", "51.55: 1\n", "80.88: 1\n", "31.38: 2\n", "91.77: 1\n", "28.31: 1\n", "88.15: 1\n", "32.51: 2\n", "44.8: 1\n", "81.32: 1\n", "231.95: 1\n", "117.71: 1\n", "11.43: 2\n", "110.69: 1\n", "99.69: 2\n", "42.37: 1\n", "335.52: 1\n", "198.27: 1\n", "131.04: 1\n", "18.65: 1\n", "30.81: 1\n", "13.47: 1\n", "9.15: 1\n", "215.66: 1\n", "85.55: 1\n", "17.44: 1\n", "20.88: 1\n", "54.75: 1\n", "38.98: 1\n", "30.98: 1\n", "63.71: 1\n", "21.7: 1\n", "101.49: 1\n", "70.15: 1\n", "28.57: 1\n", "141.19: 1\n", "9.4: 2\n", "14.62: 1\n", "99.13: 1\n", "18.29: 1\n", "185.94: 1\n", "147.88: 1\n", "205.28: 1\n", "430.03: 1\n", "15.68: 1\n", "55.48: 2\n", "18.05: 2\n", "48.73: 1\n", "67.67: 1\n", "90.5: 1\n", "118.77: 1\n", "335.32: 1\n", "32.84: 1\n", "42.59: 1\n", "17.53: 1\n", "26.92: 1\n", "39.16: 1\n", "125.15: 1\n", "228.9: 1\n", "15.48: 1\n", "216.99: 1\n", "22.7: 1\n", "68.02: 1\n", "472.66: 1\n", "74.77: 2\n", "258.62: 1\n", "38.46: 1\n", "122.16: 1\n", "15.19: 1\n", "334.09: 1\n", "86.58: 1\n", "15.96: 2\n", "61.06: 1\n", "48.98: 1\n", "113.07: 1\n", "155.92: 1\n", "147.55: 1\n", "81.26: 1\n", "327.71: 1\n", "72.2: 1\n", "125.98: 1\n", "25.16: 1\n", "122.55: 1\n", "225.39: 1\n", "15.0: 1\n", "285.85: 1\n", "80.31: 2\n", "284.61: 1\n", "270.47: 1\n", "134.31: 1\n", "34.61: 1\n", "210.56: 2\n", "216.9: 1\n", "137.56: 1\n", "130.76: 1\n", "120.68: 1\n", "42.15: 1\n", "43.01: 1\n", "232.9: 1\n", "47.98: 1\n", "366.7: 1\n", "308.02: 1\n", "209.35: 1\n", "40.61: 1\n", "380.3: 1\n", "36.62: 1\n", "282.64: 2\n", "105.08: 1\n", "55.36: 1\n", "146.56: 1\n", "27.53: 1\n", "90.73: 1\n", "8.66: 1\n", "201.51: 1\n", "174.45: 1\n", "224.05: 1\n", "45.29: 1\n", "27.73: 1\n", "74.83: 1\n", "24.05: 1\n", "121.67: 1\n", "33.25: 1\n", "358.02: 1\n", "3.73: 1\n", "101.07: 2\n", "184.11: 1\n", "16.55: 1\n", "232.85: 1\n", "110.09: 1\n", "115.28: 1\n", "179.95: 1\n", "58.32: 1\n", "205.14: 1\n", "31.06: 1\n", "109.0: 3\n", "320.52: 1\n", "14.6: 2\n", "155.94: 1\n", "293.39: 1\n", "342.29: 1\n", "389.34: 1\n", "16.71: 1\n", "152.77: 1\n", "100.56: 1\n", "115.31: 1\n", "165.5: 1\n", "128.35: 1\n", "83.43: 2\n", "28.66: 1\n", "137.29: 1\n", "208.82: 1\n", "174.68: 2\n", "102.81: 1\n", "287.63: 1\n", "119.28: 1\n", "100.7: 1\n", "20.7: 1\n", "7.02: 1\n", "173.1: 1\n", "28.77: 1\n", "18.32: 1\n", "243.61: 1\n", "147.73: 1\n", "136.24: 1\n", "14.95: 1\n", "144.71: 1\n", "75.64: 1\n", "144.78: 1\n", "127.75: 1\n", "174.52: 1\n", "133.78: 1\n", "145.96: 1\n", "103.81: 1\n", "25.28: 1\n", "19.2: 1\n", "22.09: 1\n", "29.66: 2\n", "66.07: 1\n", "117.05: 1\n", "22.91: 1\n", "21.81: 1\n", "20.48: 1\n", "19.24: 1\n", "89.39: 1\n", "13.32: 1\n", "54.97: 1\n", "111.54: 2\n", "151.9: 1\n", "36.17: 1\n", "205.54: 1\n", "57.44: 1\n", "37.67: 1\n", "197.45: 1\n", "57.67: 2\n", "36.78: 1\n", "325.56: 1\n", "188.0: 1\n", "47.44: 1\n", "94.33: 1\n", "28.16: 1\n", "11.01: 1\n", "60.82: 1\n", "87.31: 1\n", "39.0: 1\n", "146.7: 2\n", "7.32: 1\n", "30.85: 1\n", "171.13: 1\n", "128.84: 1\n", "68.32: 1\n", "176.01: 1\n", "58.33: 1\n", "67.26: 2\n", "206.31: 1\n", "25.09: 1\n", "157.21: 1\n", "65.2: 1\n", "62.78: 1\n", "137.89: 1\n", "75.32: 1\n", "27.32: 1\n", "36.85: 2\n", "16.24: 1\n", "113.67: 1\n", "18.39: 1\n", "1.61: 1\n", "3.76: 1\n", "452.64: 1\n", "6.72: 1\n", "21.36: 1\n", "135.53: 1\n", "87.55: 1\n", "23.45: 1\n", "19.39: 1\n", "179.58: 1\n", "340.63: 1\n", "265.31: 1\n", "33.03: 1\n", "131.67: 1\n", "64.71: 1\n", "18.78: 1\n", "112.23: 1\n", "65.17: 1\n", "42.14: 2\n", "330.59: 1\n", "147.61: 1\n", "18.03: 1\n", "142.78: 1\n", "19.86: 1\n", "28.39: 1\n", "25.23: 2\n", "134.25: 1\n", "128.32: 1\n", "23.82: 1\n", "129.34: 1\n", "39.39: 1\n", "95.95: 1\n", "79.73: 1\n", "69.5: 1\n", "34.62: 1\n", "263.37: 1\n", "89.25: 1\n", "154.01: 1\n", "94.31: 1\n", "10.95: 1\n", "9.42: 1\n", "21.46: 2\n", "89.97: 1\n", "22.44: 1\n", "192.51: 1\n", "6.89: 1\n", "174.73: 1\n", "31.44: 1\n", "116.71: 1\n", "196.01: 1\n", "2.69: 1\n", "137.09: 1\n", "157.0: 1\n", "116.36: 1\n", "153.0: 1\n", "204.69: 1\n", "132.26: 1\n", "162.15: 1\n", "71.26: 1\n", "216.36: 1\n", "357.77: 1\n", "40.91: 1\n", "27.13: 1\n", "137.13: 1\n", "92.35: 2\n", "350.55: 1\n", "119.4: 1\n", "164.27: 1\n", "191.51: 1\n", "89.84: 1\n", "258.82: 1\n", "116.38: 1\n", "115.1: 1\n", "383.62: 1\n", "48.61: 2\n", "133.32: 1\n", "212.22: 1\n", "124.02: 1\n", "263.7: 1\n", "22.79: 1\n", "86.34: 1\n", "165.2: 1\n", "28.99: 2\n", "44.58: 1\n", "240.44: 1\n", "19.4: 1\n", "218.82: 1\n", "163.23: 1\n", "138.33: 1\n", "316.41: 1\n", "14.29: 1\n", "30.47: 1\n", "41.68: 2\n", "19.43: 1\n", "107.42: 2\n", "200.83: 1\n", "53.23: 1\n", "264.72: 1\n", "33.8: 2\n", "83.58: 1\n", "280.03: 1\n", "152.46: 1\n", "49.19: 2\n", "58.13: 1\n", "67.59: 1\n", "67.44: 1\n", "11.81: 2\n", "120.71: 1\n", "116.41: 1\n", "264.07: 1\n", "77.97: 1\n", "140.18: 1\n", "255.38: 1\n", "172.26: 1\n", "160.15: 1\n", "51.51: 3\n", "256.51: 1\n", "137.83: 1\n", "56.25: 1\n", "382.71: 1\n", "43.56: 1\n", "113.12: 2\n", "325.6: 1\n", "18.16: 1\n", "318.75: 1\n", "42.77: 1\n", "19.01: 1\n", "14.53: 1\n", "193.9: 1\n", "279.28: 1\n", "4.09: 1\n", "22.47: 2\n", "12.06: 1\n", "196.51: 1\n", "288.93: 1\n", "5.4: 2\n", "99.53: 1\n", "177.46: 1\n", "43.57: 2\n", "216.6: 1\n", "93.5: 1\n", "118.01: 2\n", "280.52: 1\n", "273.61: 1\n", "137.54: 1\n", "62.92: 1\n", "146.24: 1\n", "90.69: 1\n", "95.14: 1\n", "90.37: 1\n", "155.15: 1\n", "16.31: 1\n", "84.86: 1\n", "169.5: 1\n", "88.8: 1\n", "30.28: 1\n", "49.03: 1\n", "91.16: 1\n", "22.65: 1\n", "191.93: 2\n", "16.75: 1\n", "20.04: 1\n", "65.51: 1\n", "343.25: 1\n", "90.53: 1\n", "215.96: 1\n", "102.06: 1\n", "181.57: 1\n", "240.4: 1\n", "106.37: 1\n", "141.86: 1\n", "96.42: 1\n", "20.65: 1\n", "178.35: 1\n", "116.98: 1\n", "92.07: 1\n", "104.49: 1\n", "17.43: 2\n", "26.39: 1\n", "5.19: 2\n", "21.76: 1\n", "33.3: 1\n", "143.21: 1\n", "95.63: 1\n", "31.29: 1\n", "200.94: 1\n", "24.96: 3\n", "91.47: 1\n", "141.46: 2\n", "19.21: 2\n", "163.95: 1\n", "16.76: 1\n", "173.44: 1\n", "225.04: 1\n", "38.63: 1\n", "67.43: 1\n", "53.43: 2\n", "204.74: 1\n", "20.68: 1\n", "131.83: 1\n", "109.51: 1\n", "73.97: 2\n", "19.93: 1\n", "175.49: 1\n", "268.26: 1\n", "185.92: 1\n", "72.43: 1\n", "16.47: 1\n", "4.77: 1\n", "70.94: 1\n", "34.73: 1\n", "139.26: 1\n", "136.32: 1\n", "283.88: 1\n", "24.12: 1\n", "79.48: 1\n", "35.32: 1\n", "153.93: 1\n", "128.1: 1\n", "324.38: 1\n", "106.17: 1\n", "6.31: 1\n", "398.09: 1\n", "59.87: 1\n", "235.14: 1\n", "183.18: 1\n", "231.81: 1\n", "129.35: 1\n", "98.95: 1\n", "22.26: 1\n", "162.57: 1\n", "184.33: 1\n", "149.78: 1\n", "14.08: 1\n", "233.16: 1\n", "76.6: 2\n", "46.95: 1\n", "89.98: 1\n", "279.09: 1\n", "23.06: 1\n", "42.7: 1\n", "83.7: 2\n", "184.91: 1\n", "8.09: 1\n", "69.45: 1\n", "70.9: 1\n", "93.73: 2\n", "147.93: 1\n", "172.53: 1\n", "77.31: 1\n", "202.35: 1\n", "374.95: 1\n", "30.5: 1\n", "32.85: 1\n", "31.11: 1\n", "19.66: 1\n", "215.85: 1\n", "53.47: 1\n", "14.89: 1\n", "183.88: 1\n", "109.87: 1\n", "11.66: 1\n", "14.58: 1\n", "67.03: 1\n", "448.08: 1\n", "59.57: 1\n", "5.82: 1\n", "15.81: 1\n", "93.57: 1\n", "81.11: 2\n", "85.2: 1\n", "12.65: 1\n", "110.33: 1\n", "63.45: 1\n", "11.95: 1\n", "71.57: 1\n", "337.26: 1\n", "50.61: 1\n", "66.86: 1\n", "172.95: 1\n", "15.85: 1\n", "102.65: 1\n", "229.39: 1\n", "159.15: 1\n", "11.76: 1\n", "13.91: 1\n", "79.03: 1\n", "25.3: 1\n", "394.41: 1\n", "84.35: 1\n", "30.89: 1\n", "73.78: 2\n", "102.37: 2\n", "246.07: 1\n", "39.09: 1\n", "83.59: 1\n", "172.02: 1\n", "16.34: 1\n", "199.35: 1\n", "344.72: 1\n", "70.48: 1\n", "477.54: 1\n", "219.33: 1\n", "85.1: 1\n", "323.63: 1\n", "353.06: 1\n", "52.9: 1\n", "159.83: 1\n", "118.32: 1\n", "320.58: 1\n", "315.61: 1\n", "45.81: 1\n", "400.73: 1\n", "42.56: 1\n", "67.47: 1\n", "239.88: 1\n", "112.5: 1\n", "172.21: 1\n", "100.02: 1\n", "168.67: 2\n", "265.84: 1\n", "197.03: 1\n", "51.58: 1\n", "26.02: 1\n", "96.62: 1\n", "197.31: 1\n", "698.77: 1\n", "585.38: 1\n", "168.61: 1\n", "296.04: 1\n", "296.32: 1\n", "94.72: 1\n", "187.45: 1\n", "118.31: 1\n", "203.07: 1\n", "241.25: 1\n", "65.49: 1\n", "144.87: 1\n", "9.29: 1\n", "296.22: 1\n", "103.15: 2\n", "166.09: 1\n", "350.36: 1\n", "198.21: 1\n", "271.48: 1\n", "162.44: 1\n", "331.89: 1\n", "270.44: 1\n", "338.5: 1\n", "107.68: 2\n", "162.22: 1\n", "97.08: 1\n", "51.98: 1\n", "76.2: 1\n", "41.47: 1\n", "13.23: 1\n", "25.31: 1\n", "181.82: 1\n", "11.09: 1\n", "92.42: 1\n", "111.79: 1\n", "403.83: 1\n", "72.14: 2\n", "302.25: 1\n", "110.85: 1\n", "45.13: 1\n", "49.48: 2\n", "348.76: 1\n", "157.35: 1\n", "187.4: 1\n", "51.76: 1\n", "258.57: 1\n", "301.08: 1\n", "37.24: 1\n", "190.78: 1\n", "274.15: 1\n", "41.67: 1\n", "138.34: 1\n", "153.17: 1\n", "116.83: 1\n", "93.05: 1\n", "217.37: 1\n", "331.29: 1\n", "172.44: 1\n", "170.78: 1\n", "87.9: 1\n", "133.39: 2\n", "112.97: 1\n", "136.79: 1\n", "62.35: 2\n", "70.1: 1\n", "253.16: 1\n", "30.16: 1\n", "319.21: 1\n", "358.7: 1\n", "147.16: 1\n", "544.78: 1\n", "23.54: 1\n", "383.66: 1\n", "573.72: 1\n", "55.64: 1\n", "25.56: 1\n", "106.7: 1\n", "223.51: 2\n", "195.13: 1\n", "96.18: 1\n", "59.81: 1\n", "375.23: 1\n", "281.17: 1\n", "75.42: 1\n", "102.59: 1\n", "60.19: 1\n", "23.85: 1\n", "225.47: 1\n", "103.16: 1\n", "145.36: 1\n", "195.26: 1\n", "621.87: 1\n", "366.57: 1\n", "105.64: 1\n", "360.52: 1\n", "131.86: 1\n", "173.96: 1\n", "101.45: 2\n", "98.93: 1\n", "84.1: 1\n", "243.94: 1\n", "240.14: 1\n", "156.39: 1\n", "226.12: 1\n", "182.66: 1\n", "270.68: 1\n", "308.18: 1\n", "217.35: 1\n", "28.76: 1\n", "94.66: 1\n", "142.04: 1\n", "254.52: 1\n", "169.42: 1\n", "69.41: 1\n", "182.55: 2\n", "129.66: 1\n", "442.36: 1\n", "71.46: 1\n", "276.5: 1\n", "234.12: 1\n", "201.28: 1\n", "117.69: 1\n", "253.58: 2\n", "513.79: 1\n", "50.19: 1\n", "48.09: 1\n", "54.72: 1\n", "178.01: 2\n", "365.44: 1\n", "176.52: 1\n", "543.79: 1\n", "171.78: 1\n", "30.95: 1\n", "74.69: 1\n", "290.45: 1\n", "284.63: 1\n", "76.57: 1\n", "77.18: 2\n", "42.3: 1\n", "101.89: 1\n", "196.84: 1\n", "144.33: 1\n", "345.63: 1\n", "145.49: 2\n", "150.77: 1\n", "420.01: 1\n", "235.16: 1\n", "270.65: 1\n", "65.25: 1\n", "293.91: 1\n", "169.18: 1\n", "65.48: 1\n", "214.39: 1\n", "347.81: 1\n", "236.77: 1\n", "146.55: 2\n", "49.25: 1\n", "177.8: 1\n", "178.41: 1\n", "647.17: 1\n", "71.76: 2\n", "197.62: 1\n", "124.01: 1\n", "546.29: 1\n", "397.13: 1\n", "128.48: 1\n", "351.29: 2\n", "174.16: 3\n", "462.62: 1\n", "302.21: 1\n", "27.44: 1\n", "352.7: 1\n", "83.15: 1\n", "82.43: 1\n", "79.59: 1\n", "112.84: 1\n", "195.53: 1\n", "131.62: 1\n", "271.3: 1\n", "143.17: 1\n", "417.39: 1\n", "363.47: 1\n", "317.68: 1\n", "177.83: 1\n", "232.37: 1\n", "54.66: 1\n", "50.13: 1\n", "235.44: 1\n", "208.55: 1\n", "347.67: 1\n", "111.33: 1\n", "31.76: 1\n", "290.03: 1\n", "46.31: 1\n", "154.45: 2\n", "401.05: 1\n", "158.77: 2\n", "192.68: 1\n", "399.89: 1\n", "289.52: 1\n", "217.3: 1\n", "196.97: 1\n", "406.67: 1\n", "129.14: 1\n", "84.82: 2\n", "50.08: 1\n", "358.16: 1\n", "278.85: 3\n", "155.58: 1\n", "113.62: 1\n", "153.21: 1\n", "138.21: 1\n", "180.65: 2\n", "54.29: 1\n", "136.03: 1\n", "116.67: 1\n", "44.81: 1\n", "353.47: 1\n", "219.03: 1\n", "74.15: 1\n", "64.42: 1\n", "406.18: 1\n", "28.98: 1\n", "269.22: 1\n", "91.68: 1\n", "55.65: 1\n", "328.83: 2\n", "421.96: 1\n", "233.48: 1\n", "70.04: 1\n", "62.7: 2\n", "211.2: 2\n", "170.19: 1\n", "429.35: 2\n", "109.79: 1\n", "94.67: 1\n", "158.62: 1\n", "341.16: 1\n", "171.96: 2\n", "154.73: 2\n", "58.27: 2\n", "185.97: 1\n", "187.39: 1\n", "195.24: 1\n", "45.87: 1\n", "47.16: 1\n", "45.98: 1\n", "270.45: 1\n", "176.91: 1\n", "40.81: 1\n", "99.57: 1\n", "46.19: 1\n", "587.66: 1\n", "42.96: 1\n", "227.25: 1\n", "174.27: 2\n", "189.29: 2\n", "88.3: 2\n", "90.17: 1\n", "238.58: 1\n", "382.81: 1\n", "151.84: 1\n", "434.48: 1\n", "30.12: 1\n", "196.81: 1\n", "79.52: 2\n", "394.2: 1\n", "485.39: 1\n", "15.52: 1\n", "88.56: 1\n", "78.77: 2\n", "183.47: 1\n", "128.63: 2\n", "266.72: 1\n", "271.74: 1\n", "69.99: 1\n", "371.14: 1\n", "175.45: 1\n", "76.5: 1\n", "184.3: 1\n", "71.4: 2\n", "295.12: 1\n", "117.4: 1\n", "484.62: 1\n", "130.32: 1\n", "369.33: 1\n", "518.92: 1\n", "123.86: 1\n", "20.52: 1\n", "496.43: 1\n", "54.41: 1\n", "127.02: 1\n", "9.79: 1\n", "99.12: 1\n", "20.37: 1\n", "211.19: 1\n", "280.61: 1\n", "54.54: 1\n", "136.73: 1\n", "80.97: 1\n", "231.65: 2\n", "193.91: 1\n", "162.25: 1\n", "629.59: 1\n", "237.0: 1\n", "190.04: 1\n", "33.18: 1\n", "227.44: 1\n", "18.25: 1\n", "202.19: 1\n", "153.49: 1\n", "88.38: 1\n", "218.26: 1\n", "54.35: 1\n", "110.86: 2\n", "278.64: 1\n", "370.74: 1\n", "140.92: 1\n", "109.59: 1\n", "97.1: 1\n", "124.04: 1\n", "120.23: 2\n", "185.44: 1\n", "55.81: 1\n", "99.92: 1\n", "344.12: 1\n", "106.15: 3\n", "66.78: 1\n", "177.12: 1\n", "236.66: 1\n", "172.03: 2\n", "152.3: 2\n", "29.52: 2\n", "163.73: 1\n", "283.26: 1\n", "162.51: 1\n", "152.66: 1\n", "178.74: 1\n", "290.68: 1\n", "57.01: 2\n", "71.12: 1\n", "163.58: 1\n", "280.74: 1\n", "12.54: 1\n", "117.11: 2\n", "172.73: 1\n", "394.25: 1\n", "91.19: 1\n", "634.24: 1\n", "13.4: 3\n", "147.76: 1\n", "154.96: 2\n", "209.59: 1\n", "49.46: 1\n", "452.17: 1\n", "505.51: 1\n", "129.38: 1\n", "107.57: 1\n", "182.39: 2\n", "300.91: 1\n", "43.49: 1\n", "159.4: 1\n", "145.23: 1\n", "378.79: 1\n", "148.56: 1\n", "245.17: 1\n", "187.44: 1\n", "155.98: 1\n", "307.16: 1\n", "207.55: 1\n", "375.78: 1\n", "48.57: 1\n", "568.6: 1\n", "107.3: 2\n", "334.72: 1\n", "432.76: 1\n", "140.8: 2\n", "186.31: 1\n", "29.48: 1\n", "465.08: 1\n", "320.36: 1\n", "126.74: 1\n", "769.4: 1\n", "13.37: 1\n", "439.52: 1\n", "261.8: 1\n", "203.66: 1\n", "243.76: 1\n", "152.08: 1\n", "217.0: 1\n", "245.79: 1\n", "208.64: 1\n", "424.19: 1\n", "176.63: 1\n", "160.32: 1\n", "124.46: 1\n", "105.22: 1\n", "77.35: 2\n", "326.35: 1\n", "269.35: 1\n", "379.92: 1\n", "175.34: 1\n", "79.84: 1\n", "298.03: 1\n", "64.39: 2\n", "33.83: 1\n", "45.6: 1\n", "87.91: 1\n", "184.38: 1\n", "279.22: 1\n", "224.37: 1\n", "25.68: 1\n", "65.66: 1\n", "23.19: 1\n", "71.18: 2\n", "233.67: 1\n", "769.05: 1\n", "171.99: 1\n", "57.7: 1\n", "97.56: 1\n", "272.32: 1\n", "58.12: 1\n", "298.06: 1\n", "157.08: 1\n", "13.82: 1\n", "232.53: 1\n", "39.14: 2\n", "190.77: 1\n", "271.5: 2\n", "270.56: 2\n", "120.8: 1\n", "159.71: 1\n", "200.32: 1\n", "122.19: 1\n", "220.8: 1\n", "128.26: 1\n", "81.94: 2\n", "114.35: 1\n", "354.34: 1\n", "87.0: 1\n", "609.87: 1\n", "282.92: 1\n", "400.35: 1\n", "85.02: 2\n", "476.57: 1\n", "504.1: 1\n", "89.76: 1\n", "109.44: 1\n", "383.56: 1\n", "45.52: 1\n", "828.49: 1\n", "34.87: 1\n", "399.11: 1\n", "597.34: 1\n", "129.12: 1\n", "170.51: 1\n", "21.05: 1\n", "379.77: 1\n", "21.74: 1\n", "125.75: 2\n", "272.94: 1\n", "59.18: 1\n", "192.2: 1\n", "77.22: 2\n", "139.08: 2\n", "145.18: 1\n", "335.84: 1\n", "56.61: 2\n", "237.72: 1\n", "197.27: 1\n", "127.94: 1\n", "158.59: 1\n", "299.54: 1\n", "84.67: 1\n", "345.01: 1\n", "724.57: 1\n", "181.01: 1\n", "261.82: 1\n", "31.75: 1\n", "31.43: 1\n", "189.38: 1\n", "206.47: 1\n", "149.51: 1\n", "122.07: 1\n", "60.59: 1\n", "17.86: 1\n", "155.14: 1\n", "242.4: 1\n", "87.49: 1\n", "170.73: 1\n", "99.21: 1\n", "410.22: 1\n", "156.81: 1\n", "165.86: 1\n", "344.0: 1\n", "45.88: 1\n", "105.27: 1\n", "103.32: 1\n", "103.36: 3\n", "15.93: 1\n", "244.29: 1\n", "237.74: 1\n", "240.64: 1\n", "260.66: 1\n", "229.33: 1\n", "69.72: 1\n", "25.75: 1\n", "11.53: 1\n", "117.08: 1\n", "6.14: 1\n", "129.95: 1\n", "119.42: 1\n", "781.05: 1\n", "157.19: 1\n", "231.56: 1\n", "372.62: 1\n", "109.11: 1\n", "289.75: 1\n", "165.12: 1\n", "124.77: 1\n", "151.27: 1\n", "44.65: 2\n", "82.03: 1\n", "167.52: 1\n", "108.76: 1\n", "160.75: 1\n", "112.4: 1\n", "133.45: 1\n", "237.28: 1\n", "221.13: 1\n", "263.06: 1\n", "67.22: 1\n", "28.15: 2\n", "436.92: 1\n", "98.33: 1\n", "320.16: 1\n", "55.0: 1\n", "475.13: 1\n", "547.16: 1\n", "316.0: 1\n", "27.25: 1\n", "180.73: 2\n", "66.19: 1\n", "236.62: 1\n", "119.43: 2\n", "333.57: 1\n", "344.01: 1\n", "34.67: 1\n", "98.73: 1\n", "106.81: 1\n", "247.25: 1\n", "42.97: 1\n", "75.78: 1\n", "455.53: 1\n", "47.83: 1\n", "171.17: 1\n", "43.69: 2\n", "331.75: 1\n", "55.45: 1\n", "17.25: 1\n", "241.4: 1\n", "143.19: 1\n", "392.21: 1\n", "110.72: 1\n", "130.41: 1\n", "351.37: 1\n", "27.7: 1\n", "145.79: 1\n", "429.04: 1\n", "269.66: 1\n", "64.64: 1\n", "61.49: 1\n", "55.12: 1\n", "195.11: 1\n", "33.26: 1\n", "155.66: 1\n", "104.15: 1\n", "26.1: 1\n", "112.59: 2\n", "210.34: 1\n", "95.82: 2\n", "39.45: 3\n", "205.01: 1\n", "82.58: 1\n", "325.05: 1\n", "127.49: 1\n", "162.86: 1\n", "71.81: 1\n", "44.35: 1\n", "306.75: 1\n", "29.22: 1\n", "56.21: 1\n", "463.15: 2\n", "167.81: 2\n", "533.18: 1\n", "269.18: 1\n", "236.22: 1\n", "466.59: 1\n", "391.03: 1\n", "119.15: 1\n", "49.59: 1\n", "342.39: 1\n", "129.94: 2\n", "394.14: 1\n", "113.32: 1\n", "259.38: 1\n", "166.07: 1\n", "27.84: 1\n", "185.43: 1\n", "64.43: 1\n", "113.09: 1\n", "150.9: 1\n", "232.77: 1\n", "133.21: 1\n", "105.28: 1\n", "67.94: 1\n", "83.98: 1\n", "172.84: 1\n", "279.63: 1\n", "63.41: 2\n", "40.71: 1\n", "160.19: 1\n", "268.1: 1\n", "128.52: 1\n", "152.63: 1\n", "257.53: 2\n", "165.6: 1\n", "38.74: 1\n", "564.24: 1\n", "333.45: 1\n", "225.69: 1\n", "228.4: 1\n", "346.24: 1\n", "120.17: 1\n", "147.57: 1\n", "168.4: 1\n", "329.64: 1\n", "148.71: 1\n", "204.36: 1\n", "273.29: 1\n", "50.99: 1\n", "394.79: 1\n", "75.35: 2\n", "52.53: 1\n", "21.15: 1\n", "208.25: 1\n", "32.71: 1\n", "475.05: 1\n", "298.5: 1\n", "247.84: 1\n", "317.03: 1\n", "99.26: 1\n", "240.1: 1\n", "43.28: 1\n", "209.41: 1\n", "220.99: 1\n", "554.8: 1\n", "344.32: 1\n", "171.85: 1\n", "550.84: 1\n", "95.53: 1\n", "156.74: 1\n", "199.12: 1\n", "215.88: 1\n", "218.65: 1\n", "167.11: 1\n", "513.22: 1\n", "127.03: 2\n", "569.22: 1\n", "131.78: 1\n", "498.03: 1\n", "152.93: 1\n", "238.43: 1\n", "200.11: 1\n", "259.81: 1\n", "73.25: 1\n", "194.25: 1\n", "98.41: 1\n", "208.47: 1\n", "163.11: 1\n", "196.0: 1\n", "61.39: 1\n", "212.66: 1\n", "560.86: 1\n", "350.1: 1\n", "209.89: 1\n", "196.16: 1\n", "86.59: 1\n", "367.9: 1\n", "116.23: 1\n", "23.29: 1\n", "9.1: 1\n", "133.7: 1\n", "367.55: 1\n", "249.42: 1\n", "79.42: 1\n", "250.66: 1\n", "303.1: 1\n", "333.75: 1\n", "586.77: 1\n", "257.76: 1\n", "481.27: 1\n", "208.44: 1\n", "14.21: 2\n", "102.17: 1\n", "322.55: 1\n", "152.4: 1\n", "43.26: 1\n", "371.92: 1\n", "41.62: 1\n", "163.71: 1\n", "284.27: 1\n", "93.77: 1\n", "277.87: 1\n", "86.81: 1\n", "67.99: 1\n", "439.31: 1\n", "236.65: 1\n", "765.83: 1\n", "473.45: 1\n", "562.23: 1\n", "74.54: 1\n", "418.51: 1\n", "63.24: 1\n", "77.54: 1\n", "183.73: 1\n", "38.77: 1\n", "142.56: 1\n", "321.08: 1\n", "75.65: 1\n", "263.14: 1\n", "15.56: 1\n", "173.91: 1\n", "161.8: 1\n", "519.8: 1\n", "481.92: 1\n", "297.61: 1\n", "295.75: 1\n", "97.43: 1\n", "31.92: 1\n", "923.4: 1\n", "60.38: 1\n", "127.86: 1\n", "189.22: 1\n", "259.66: 1\n", "113.25: 1\n", "428.52: 1\n", "241.08: 1\n", "111.07: 1\n", "429.33: 1\n", "100.89: 1\n", "56.4: 1\n", "343.75: 1\n", "130.55: 1\n", "132.38: 1\n", "165.07: 1\n", "143.14: 1\n", "78.67: 1\n", "228.73: 1\n", "135.42: 1\n", "151.89: 1\n", "636.56: 1\n", "311.1: 1\n", "328.19: 1\n", "245.39: 1\n", "364.33: 1\n", "63.21: 2\n", "172.17: 1\n", "399.67: 1\n", "180.21: 1\n", "174.03: 1\n", "221.33: 1\n", "81.97: 1\n", "358.76: 1\n", "195.2: 1\n", "467.25: 2\n", "44.76: 1\n", "269.06: 1\n", "219.72: 1\n", "34.2: 1\n", "370.94: 1\n", "577.66: 1\n", "30.26: 1\n", "223.27: 1\n", "53.09: 1\n", "238.36: 1\n", "479.74: 1\n", "124.86: 1\n", "304.24: 1\n", "254.19: 1\n", "45.94: 1\n", "87.61: 1\n", "375.38: 1\n", "171.41: 1\n", "168.84: 1\n", "64.69: 1\n", "69.47: 2\n", "334.8: 1\n", "106.79: 2\n", "87.8: 1\n", "33.21: 1\n", "395.76: 1\n", "44.85: 1\n", "823.69: 1\n", "305.85: 1\n", "159.87: 1\n", "289.15: 1\n", "150.25: 1\n", "305.87: 1\n", "99.98: 1\n", "395.23: 1\n", "389.87: 1\n", "279.98: 1\n", "181.56: 1\n", "142.69: 1\n", "218.0: 1\n", "173.17: 1\n", "12.87: 1\n", "101.53: 1\n", "211.13: 1\n", "60.56: 1\n", "217.28: 1\n", "206.4: 1\n", "315.97: 1\n", "262.92: 2\n", "439.96: 1\n", "144.8: 1\n", "248.09: 1\n", "499.63: 1\n", "49.32: 1\n", "208.01: 1\n", "136.38: 1\n", "192.24: 1\n", "198.75: 1\n", "376.42: 1\n", "105.17: 1\n", "32.44: 1\n", "41.69: 1\n", "702.7: 1\n", "54.92: 3\n", "226.9: 1\n", "328.8: 1\n", "169.15: 1\n", "12.17: 1\n", "230.63: 1\n", "55.5: 1\n", "142.42: 1\n", "324.0: 1\n", "422.46: 1\n", "47.97: 3\n", "97.3: 2\n", "280.67: 1\n", "95.41: 1\n", "241.13: 1\n", "322.65: 1\n", "74.05: 1\n", "204.6: 1\n", "677.49: 1\n", "33.74: 1\n", "44.69: 1\n", "156.96: 1\n", "197.56: 1\n", "16.4: 1\n", "62.81: 1\n", "180.49: 1\n", "244.0: 2\n", "116.78: 1\n", "80.48: 1\n", "51.54: 1\n", "114.18: 1\n", "549.63: 1\n", "209.11: 1\n", "73.43: 1\n", "120.04: 1\n", "110.51: 1\n", "92.54: 1\n", "66.8: 1\n", "154.19: 1\n", "85.52: 1\n", "349.06: 1\n", "240.7: 1\n", "331.82: 1\n", "189.06: 1\n", "272.15: 1\n", "225.17: 1\n", "60.83: 1\n", "215.04: 1\n", "688.94: 1\n", "393.51: 1\n", "54.02: 1\n", "38.51: 1\n", "310.24: 1\n", "253.96: 1\n", "101.97: 1\n", "372.87: 1\n", "188.65: 1\n", "501.43: 1\n", "502.77: 1\n", "665.06: 1\n", "181.07: 1\n", "253.5: 1\n", "242.43: 1\n", "37.34: 1\n", "149.55: 1\n", "266.19: 1\n", "459.02: 1\n", "35.03: 1\n", "96.93: 1\n", "95.84: 1\n", "310.57: 1\n", "167.19: 1\n", "133.68: 2\n", "107.32: 1\n", "46.28: 1\n", "327.17: 1\n", "221.37: 1\n", "117.45: 1\n", "267.58: 1\n", "656.8: 1\n", "40.89: 1\n", "142.58: 1\n", "182.95: 1\n", "581.66: 1\n", "156.94: 1\n", "123.82: 2\n", "97.17: 1\n", "469.88: 1\n", "259.97: 2\n", "19.47: 1\n", "129.86: 1\n", "688.96: 1\n", "288.28: 1\n", "121.76: 1\n", "84.14: 1\n", "246.6: 1\n", "65.16: 1\n", "206.82: 1\n", "22.35: 1\n", "794.41: 1\n", "893.63: 1\n", "153.92: 1\n", "225.77: 1\n", "278.45: 1\n", "43.73: 1\n", "196.54: 1\n", "50.1: 1\n", "450.3: 1\n", "695.52: 1\n", "88.78: 1\n", "305.43: 1\n", "45.39: 1\n", "262.22: 1\n", "184.25: 1\n", "448.28: 1\n", "108.87: 1\n", "110.84: 1\n", "358.56: 1\n", "71.21: 1\n", "77.15: 2\n", "131.68: 1\n", "141.97: 1\n", "315.91: 1\n", "136.29: 1\n", "355.53: 1\n", "74.35: 1\n", "65.89: 1\n", "72.13: 1\n", "41.12: 1\n", "275.27: 1\n", "390.46: 1\n", "47.47: 1\n", "239.25: 1\n", "43.99: 1\n", "193.84: 1\n", "80.29: 1\n", "511.98: 1\n", "238.52: 1\n", "435.64: 1\n", "42.32: 1\n", "602.19: 1\n", "52.24: 1\n", "334.11: 1\n", "134.0: 1\n", "128.34: 1\n", "631.19: 1\n", "341.59: 1\n", "437.83: 1\n", "274.21: 1\n", "71.5: 1\n", "718.12: 1\n", "254.42: 1\n", "182.26: 1\n", "41.56: 1\n", "32.25: 1\n", "186.9: 1\n", "115.69: 1\n", "291.16: 1\n", "33.84: 1\n", "565.31: 1\n", "133.11: 1\n", "63.13: 1\n", "450.43: 1\n", "298.68: 1\n", "286.12: 1\n", "392.46: 1\n", "645.63: 1\n", "943.94: 1\n", "45.67: 1\n", "246.74: 1\n", "438.07: 1\n", "373.78: 1\n", "60.78: 1\n", "501.25: 1\n", "394.78: 1\n", "378.62: 1\n", "145.64: 1\n", "60.97: 1\n", "232.34: 1\n", "140.52: 2\n", "219.56: 1\n", "89.1: 1\n", "46.66: 1\n", "401.44: 1\n", "112.86: 1\n", "147.71: 1\n", "394.56: 1\n", "332.87: 1\n", "306.6: 1\n", "57.77: 1\n", "529.74: 1\n", "345.04: 1\n", "446.73: 1\n", "106.63: 1\n", "106.73: 1\n", "45.08: 1\n", "461.7: 1\n", "269.01: 1\n", "410.05: 1\n", "99.32: 2\n", "465.06: 1\n", "274.51: 1\n", "423.24: 1\n", "183.3: 1\n", "41.9: 1\n", "172.81: 1\n", "600.47: 1\n", "39.62: 1\n", "241.26: 1\n", "113.98: 1\n", "211.26: 1\n", "139.83: 1\n", "755.76: 1\n", "63.19: 1\n", "297.38: 1\n", "355.76: 1\n", "102.58: 1\n", "84.77: 1\n", "37.42: 2\n", "194.69: 1\n", "48.02: 1\n", "210.14: 1\n", "89.7: 1\n", "151.86: 1\n", "234.95: 1\n", "178.7: 1\n", "121.7: 1\n", "165.62: 1\n", "143.63: 1\n", "410.24: 1\n", "56.26: 1\n", "430.45: 1\n", "163.51: 1\n", "130.25: 1\n", "253.57: 1\n", "325.07: 1\n", "94.58: 1\n", "149.18: 1\n", "343.54: 1\n", "195.91: 1\n", "43.92: 1\n", "240.59: 1\n", "239.44: 2\n", "110.55: 1\n", "38.39: 1\n", "534.51: 1\n", "34.43: 1\n", "327.72: 1\n", "81.01: 1\n", "237.68: 1\n", "167.29: 1\n", "115.42: 1\n", "372.35: 1\n", "102.66: 1\n", "233.98: 1\n", "416.89: 1\n", "60.94: 1\n", "277.64: 1\n", "575.58: 1\n", "147.9: 1\n", "147.94: 1\n", "893.05: 1\n", "103.9: 1\n", "255.44: 1\n", "229.2: 1\n", "124.62: 1\n", "53.97: 1\n", "303.9: 1\n", "131.57: 1\n", "356.18: 1\n", "44.38: 1\n", "111.52: 1\n", "807.96: 1\n", "168.75: 1\n", "108.35: 2\n", "135.43: 1\n", "55.8: 1\n", "194.99: 1\n", "69.17: 1\n", "70.2: 1\n", "549.44: 1\n", "212.92: 1\n", "66.65: 1\n", "437.53: 1\n", "642.11: 1\n", "64.06: 2\n", "102.83: 1\n", "51.6: 1\n", "819.45: 1\n", "239.7: 1\n", "488.58: 1\n", "145.39: 1\n", "297.08: 1\n", "278.24: 1\n", "201.92: 1\n", "79.54: 1\n", "242.35: 1\n", "168.04: 1\n", "160.45: 1\n", "297.7: 1\n", "395.91: 1\n", "117.12: 1\n", "189.3: 1\n", "81.05: 1\n", "117.52: 1\n", "218.14: 1\n", "370.66: 1\n", "369.43: 1\n", "423.57: 1\n", "137.16: 1\n", "360.28: 1\n", "60.85: 1\n", "304.1: 1\n", "45.38: 1\n", "440.33: 1\n", "196.95: 2\n", "243.15: 1\n", "258.8: 1\n", "187.47: 1\n", "237.89: 1\n", "137.05: 1\n", "505.05: 1\n", "87.26: 1\n", "41.25: 1\n", "215.1: 1\n", "348.22: 1\n", "215.72: 1\n", "150.46: 1\n", "197.64: 1\n", "517.74: 1\n", "229.54: 1\n", "257.56: 1\n", "260.2: 1\n", "191.35: 1\n", "390.34: 1\n", "272.13: 1\n", "88.55: 1\n", "72.22: 1\n", "160.97: 1\n", "288.01: 1\n", "211.51: 1\n", "344.69: 1\n", "737.65: 1\n", "287.13: 1\n", "742.51: 1\n", "502.72: 1\n", "188.99: 1\n", "36.87: 1\n", "282.13: 1\n", "194.92: 2\n", "112.51: 1\n", "302.03: 1\n", "550.65: 2\n", "313.41: 1\n", "57.88: 1\n", "304.61: 1\n", "361.28: 1\n", "230.73: 1\n", "417.95: 1\n", "157.54: 1\n", "487.15: 1\n", "117.87: 1\n", "136.27: 1\n", "469.34: 1\n", "382.91: 1\n", "123.2: 1\n", "19.6: 1\n", "124.03: 1\n", "151.4: 1\n", "197.23: 1\n", "85.53: 1\n", "205.22: 1\n", "32.12: 1\n", "53.99: 1\n", "334.89: 1\n", "75.76: 2\n", "198.45: 1\n", "550.83: 1\n", "102.63: 1\n", "46.1: 1\n", "37.05: 1\n", "316.52: 1\n", "54.42: 1\n", "31.65: 1\n", "34.71: 2\n", "110.28: 2\n", "407.37: 1\n", "102.74: 2\n", "658.18: 1\n", "190.4: 1\n", "167.47: 1\n", "454.3: 1\n", "121.47: 1\n", "82.16: 1\n", "82.47: 1\n", "106.87: 1\n", "199.16: 1\n", "136.74: 1\n", "366.12: 1\n", "352.58: 2\n", "279.36: 1\n", "46.58: 1\n", "640.32: 1\n", "188.76: 1\n", "326.75: 1\n", "182.23: 1\n", "396.27: 1\n", "392.1: 2\n", "280.77: 1\n", "216.14: 1\n", "550.31: 1\n", "170.06: 1\n", "358.31: 1\n", "303.12: 1\n", "43.95: 1\n", "229.75: 1\n", "57.16: 2\n", "87.66: 1\n", "392.23: 1\n", "191.02: 1\n", "183.86: 1\n", "516.22: 1\n", "200.61: 1\n", "114.31: 1\n", "257.71: 1\n", "238.57: 1\n", "44.24: 1\n", "216.18: 1\n", "285.6: 1\n", "53.61: 1\n", "87.68: 1\n", "349.2: 1\n", "241.24: 1\n", "358.51: 1\n", "74.41: 2\n", "97.07: 1\n", "531.41: 1\n", "214.7: 1\n", "43.23: 1\n", "327.03: 1\n", "172.75: 1\n", "38.41: 1\n", "256.49: 1\n", "218.77: 1\n", "369.14: 2\n", "211.48: 1\n", "114.88: 2\n", "315.31: 1\n", "343.05: 1\n", "109.8: 1\n", "171.29: 1\n", "404.57: 1\n", "57.18: 1\n", "421.03: 1\n", "448.89: 1\n", "294.61: 1\n", "158.41: 1\n", "83.27: 2\n", "70.13: 1\n", "150.38: 1\n", "360.9: 1\n", "115.03: 1\n", "255.97: 1\n", "34.44: 1\n", "56.77: 1\n", "267.98: 1\n", "206.6: 1\n", "119.06: 1\n", "440.49: 1\n", "286.51: 1\n", "85.12: 1\n", "41.48: 1\n", "369.75: 1\n", "397.3: 1\n", "54.78: 1\n", "475.31: 1\n", "23.17: 1\n", "549.68: 1\n", "116.24: 1\n", "153.71: 1\n", "63.6: 1\n", "391.04: 1\n", "37.26: 2\n", "272.51: 1\n", "409.43: 1\n", "146.09: 1\n", "265.25: 1\n", "168.91: 1\n", "114.89: 1\n", "51.8: 1\n", "327.31: 1\n", "198.1: 1\n", "74.79: 1\n", "296.48: 1\n", "226.84: 1\n", "43.63: 1\n", "87.83: 1\n", "426.63: 1\n", "156.97: 1\n", "29.91: 2\n", "279.71: 1\n", "147.7: 1\n", "196.94: 1\n", "75.23: 1\n", "97.34: 1\n", "195.98: 1\n", "51.63: 1\n", "128.69: 1\n", "456.07: 1\n", "276.48: 1\n", "106.07: 1\n", "202.41: 1\n", "157.43: 1\n", "404.58: 1\n", "24.87: 1\n", "205.25: 1\n", "115.14: 1\n", "32.21: 1\n", "697.35: 1\n", "268.87: 1\n", "456.93: 1\n", "79.45: 1\n", "218.44: 1\n", "304.41: 1\n", "436.7: 1\n", "231.08: 1\n", "259.9: 1\n", "233.78: 1\n", "129.71: 1\n", "112.29: 1\n", "248.06: 1\n", "135.17: 1\n", "207.49: 1\n", "77.49: 1\n", "81.56: 1\n", "70.19: 1\n", "165.38: 1\n", "315.08: 1\n", "180.96: 1\n", "444.64: 1\n", "487.4: 1\n", "57.52: 1\n", "601.62: 1\n", "177.23: 1\n", "125.0: 1\n", "140.75: 1\n", "98.97: 1\n", "332.32: 1\n", "84.89: 1\n", "79.63: 1\n", "145.75: 1\n", "163.26: 1\n", "302.85: 1\n", "401.36: 1\n", "38.93: 1\n", "385.71: 1\n", "139.05: 1\n", "19.5: 1\n", "67.89: 1\n", "51.97: 1\n", "80.59: 1\n", "135.38: 1\n", "254.75: 1\n", "194.57: 1\n", "159.0: 1\n", "107.65: 1\n", "109.39: 1\n", "61.48: 1\n", "51.99: 1\n", "333.26: 1\n", "381.23: 1\n", "149.57: 1\n", "94.04: 1\n", "209.71: 1\n", "162.13: 1\n", "36.96: 1\n", "158.13: 1\n", "58.71: 1\n", "436.26: 1\n", "261.46: 1\n", "36.47: 1\n", "178.62: 1\n", "49.14: 1\n", "109.74: 1\n", "773.46: 1\n", "198.22: 1\n", "21.0: 1\n", "549.03: 1\n", "124.42: 1\n", "65.78: 2\n", "170.18: 1\n", "464.56: 1\n", "461.11: 1\n", "438.37: 1\n", "65.03: 1\n", "50.11: 1\n", "139.91: 2\n", "276.62: 1\n", "35.93: 1\n", "102.57: 1\n", "84.07: 1\n", "222.44: 1\n", "143.46: 1\n", "84.42: 1\n", "152.13: 1\n", "509.37: 1\n", "578.8: 1\n", "336.08: 1\n", "53.63: 1\n", "281.53: 1\n", "262.52: 1\n", "193.57: 1\n", "266.66: 1\n", "490.57: 1\n", "354.04: 1\n", "24.74: 1\n", "277.71: 1\n", "17.94: 1\n", "228.96: 1\n", "336.56: 1\n", "432.81: 1\n", "299.12: 1\n", "384.83: 1\n", "253.83: 1\n", "157.22: 1\n", "754.03: 1\n", "163.25: 1\n", "327.55: 1\n", "84.11: 1\n", "423.93: 1\n", "100.14: 1\n", "64.83: 1\n", "15.82: 1\n", "197.83: 1\n", "49.65: 1\n", "222.26: 1\n", "212.56: 1\n", "631.68: 1\n", "43.03: 1\n", "303.28: 1\n", "279.79: 1\n", "57.74: 1\n", "565.83: 1\n", "494.35: 1\n", "191.76: 1\n", "181.14: 1\n", "301.89: 1\n", "312.5: 1\n", "283.03: 1\n", "461.82: 1\n", "153.07: 1\n", "99.91: 1\n", "388.91: 1\n", "99.45: 1\n", "168.3: 1\n", "307.41: 1\n", "147.23: 1\n", "449.59: 1\n", "305.24: 1\n", "233.58: 1\n", "468.13: 1\n", "339.13: 1\n", "143.03: 1\n", "231.03: 1\n", "193.41: 1\n", "130.71: 1\n", "164.61: 1\n", "66.67: 1\n", "194.13: 1\n", "141.24: 1\n", "297.92: 1\n", "479.81: 1\n", "200.72: 1\n", "218.33: 1\n", "89.67: 1\n", "57.75: 1\n", "47.93: 1\n", "336.02: 1\n", "127.15: 1\n", "117.95: 1\n", "76.76: 1\n", "256.36: 1\n", "171.67: 1\n", "17.71: 1\n", "327.53: 1\n", "35.63: 1\n", "196.6: 1\n", "274.24: 1\n", "383.32: 1\n", "162.41: 1\n", "441.28: 1\n", "443.63: 1\n", "68.89: 1\n", "145.66: 1\n", "161.17: 1\n", "61.41: 1\n", "536.81: 1\n", "120.69: 1\n", "157.8: 1\n", "183.49: 1\n", "259.82: 1\n", "591.73: 1\n", "224.96: 2\n", "59.0: 1\n", "386.03: 1\n", "160.82: 1\n", "44.99: 2\n", "42.76: 1\n", "257.59: 1\n", "273.21: 1\n", "186.08: 1\n", "142.35: 1\n", "84.29: 1\n", "66.69: 2\n", "253.55: 1\n", "41.44: 1\n", "243.19: 1\n", "362.2: 1\n", "255.41: 1\n", "484.9: 1\n", "130.58: 1\n", "71.61: 1\n", "43.88: 1\n", "171.15: 1\n", "16.44: 1\n", "19.51: 1\n", "75.48: 1\n", "209.73: 1\n", "17.76: 1\n", "98.7: 1\n", "36.59: 1\n", "506.71: 1\n", "173.85: 1\n", "196.79: 1\n", "206.59: 1\n", "291.27: 1\n", "98.57: 1\n", "170.48: 1\n", "212.24: 1\n", "434.47: 1\n", "10.68: 1\n", "98.12: 1\n", "80.12: 1\n", "196.98: 1\n", "71.13: 1\n", "108.07: 1\n", "86.88: 1\n", "265.72: 1\n", "439.47: 1\n", "6.25: 1\n", "59.95: 1\n", "97.41: 1\n", "200.73: 1\n", "325.06: 1\n", "312.21: 1\n", "113.9: 1\n", "227.32: 1\n", "159.57: 1\n", "56.74: 1\n", "154.61: 1\n", "49.39: 1\n", "259.29: 1\n", "548.16: 1\n", "206.2: 1\n", "332.65: 1\n", "276.68: 1\n", "479.11: 1\n", "47.02: 1\n", "74.74: 1\n", "279.46: 1\n", "29.31: 1\n", "43.33: 1\n", "204.34: 1\n", "67.96: 1\n", "153.73: 1\n", "303.66: 1\n", "129.24: 1\n", "191.7: 1\n", "64.95: 1\n", "434.4: 1\n", "195.64: 1\n", "121.73: 1\n", "690.38: 1\n", "238.31: 1\n", "140.67: 1\n", "37.52: 1\n", "221.71: 1\n", "225.6: 1\n", "219.74: 1\n", "451.11: 1\n", "217.44: 1\n", "39.52: 1\n", "93.7: 1\n", "46.63: 1\n", "222.67: 1\n", "86.67: 1\n", "256.89: 2\n", "61.85: 1\n", "242.77: 1\n", "104.0: 1\n", "218.23: 1\n", "286.01: 1\n", "57.66: 1\n", "157.31: 1\n", "459.79: 1\n", "286.76: 1\n", "36.81: 1\n", "307.66: 1\n", "360.82: 1\n", "71.16: 1\n", "303.4: 1\n", "140.33: 1\n", "322.45: 1\n", "385.07: 1\n", "298.45: 1\n", "93.97: 1\n", "122.01: 1\n", "333.14: 1\n", "128.24: 1\n", "139.21: 1\n", "74.49: 1\n", "180.95: 1\n", "226.03: 1\n", "61.23: 1\n", "319.13: 1\n", "218.59: 1\n", "278.15: 1\n", "77.91: 1\n", "261.32: 1\n", "264.28: 1\n", "93.31: 1\n", "74.59: 1\n", "131.4: 1\n", "368.88: 1\n", "56.16: 1\n", "616.59: 1\n", "20.55: 1\n", "392.98: 1\n", "92.48: 1\n", "225.84: 1\n", "153.54: 1\n", "38.18: 1\n", "450.39: 1\n", "94.11: 1\n", "340.9: 1\n", "281.21: 1\n", "371.6: 1\n", "178.57: 1\n", "80.21: 1\n", "216.64: 1\n", "153.09: 1\n", "254.56: 1\n", "278.69: 1\n", "84.98: 1\n", "200.81: 1\n", "96.27: 1\n", "586.54: 1\n", "360.63: 1\n", "187.19: 1\n", "55.41: 1\n", "299.98: 1\n", "136.4: 1\n", "61.87: 1\n", "252.35: 1\n", "303.13: 1\n", "59.55: 1\n", "252.25: 1\n", "111.08: 1\n", "290.62: 1\n", "202.78: 1\n", "109.58: 1\n", "455.56: 1\n", "452.04: 1\n", "102.21: 1\n", "227.93: 1\n", "62.1: 1\n", "513.91: 1\n", "503.76: 1\n", "105.9: 1\n", "85.57: 1\n", "219.14: 1\n", "94.57: 1\n", "45.57: 1\n", "383.06: 1\n", "71.47: 1\n", "128.79: 1\n", "189.1: 1\n", "85.49: 1\n", "69.15: 1\n", "314.97: 1\n", "205.13: 1\n", "60.12: 1\n", "634.61: 1\n", "675.98: 1\n", "136.95: 1\n", "534.03: 1\n", "289.59: 1\n", "251.75: 1\n", "273.59: 1\n", "107.78: 1\n", "146.42: 1\n", "323.78: 1\n", "152.74: 1\n", "55.17: 1\n", "159.78: 1\n", "107.46: 1\n", "451.9: 1\n", "37.49: 1\n", "187.69: 1\n", "252.21: 2\n", "252.88: 1\n", "248.2: 1\n", "92.84: 1\n", "332.63: 1\n", "208.23: 1\n", "303.78: 1\n", "509.48: 1\n", "101.03: 1\n", "105.46: 1\n", "423.73: 1\n", "502.55: 1\n", "765.86: 1\n", "273.89: 1\n", "36.09: 1\n", "277.97: 1\n", "162.64: 1\n", "125.94: 2\n", "35.2: 1\n", "301.94: 1\n", "178.19: 1\n", "88.16: 1\n", "443.22: 1\n", "79.55: 1\n", "174.01: 1\n", "275.88: 1\n", "198.84: 1\n", "160.17: 1\n", "163.27: 1\n", "99.38: 1\n", "252.59: 1\n", "610.36: 1\n", "89.03: 2\n", "69.48: 1\n", "52.0: 1\n", "619.24: 1\n", "89.58: 1\n", "180.72: 1\n", "244.6: 1\n", "61.58: 1\n", "285.13: 1\n", "699.59: 1\n", "363.14: 1\n", "71.48: 1\n", "177.56: 1\n", "78.23: 1\n", "193.12: 1\n", "296.63: 1\n", "293.93: 1\n", "123.54: 1\n", "163.72: 1\n", "329.45: 1\n", "3.58: 1\n", "62.68: 1\n", "291.35: 1\n", "357.15: 1\n", "274.91: 1\n", "218.9: 1\n", "163.69: 1\n", "198.0: 1\n", "564.7: 1\n", "53.24: 1\n", "403.75: 1\n", "363.42: 1\n", "448.9: 1\n", "88.09: 1\n", "313.02: 1\n", "449.98: 1\n", "337.35: 1\n", "144.16: 1\n", "243.11: 1\n", "688.02: 1\n", "171.77: 1\n", "233.18: 1\n", "803.55: 1\n", "34.63: 1\n", "492.84: 1\n", "154.03: 1\n", "215.03: 1\n", "148.18: 2\n", "105.88: 1\n", "139.14: 1\n", "115.24: 2\n", "67.16: 1\n", "80.47: 1\n", "50.62: 1\n", "157.76: 1\n", "77.94: 1\n", "160.4: 1\n", "155.46: 1\n", "151.31: 1\n", "69.16: 1\n", "123.69: 1\n", "136.57: 1\n", "49.3: 1\n", "109.33: 1\n", "87.89: 2\n", "88.13: 1\n", "143.36: 1\n", "177.24: 1\n", "69.86: 1\n", "106.31: 1\n", "137.2: 1\n", "54.17: 1\n", "70.83: 1\n", "247.4: 1\n", "82.63: 1\n", "45.79: 1\n", "140.32: 1\n", "75.15: 1\n", "40.03: 1\n", "75.44: 1\n", "44.14: 1\n", "151.99: 1\n", "123.58: 1\n", "38.81: 1\n", "115.96: 1\n", "81.04: 1\n", "112.44: 1\n", "80.07: 1\n", "103.71: 1\n", "50.76: 1\n", "87.57: 1\n", "127.87: 1\n", "119.5: 1\n", "80.35: 1\n", "141.66: 1\n", "33.6: 1\n", "77.88: 1\n", "65.95: 1\n", "35.33: 1\n", "36.56: 1\n", "73.4: 1\n", "31.78: 1\n", "30.22: 1\n", "30.51: 1\n", "154.71: 2\n", "37.03: 1\n", "73.18: 1\n", "17.04: 1\n", "69.62: 1\n", "92.96: 1\n", "72.48: 1\n", "66.18: 1\n", "140.74: 1\n", "118.16: 1\n", "240.49: 2\n", "39.18: 1\n", "169.69: 1\n", "111.04: 1\n", "96.36: 1\n", "119.91: 1\n", "164.75: 1\n", "125.46: 2\n", "31.03: 2\n", "109.2: 1\n", "13.03: 1\n", "245.67: 1\n", "52.74: 1\n", "122.72: 1\n", "353.96: 1\n", "211.43: 1\n", "46.79: 1\n", "48.07: 1\n", "245.73: 1\n", "8.31: 1\n", "271.41: 1\n", "189.26: 1\n", "163.97: 1\n", "243.7: 1\n", "93.72: 1\n", "40.25: 1\n", "31.85: 1\n", "10.97: 1\n", "135.91: 1\n", "58.02: 1\n", "135.49: 1\n", "39.31: 1\n", "19.69: 1\n", "66.22: 1\n", "80.46: 1\n", "114.54: 1\n", "14.63: 1\n", "17.03: 1\n", "147.69: 1\n", "239.68: 1\n", "19.99: 1\n", "50.18: 1\n", "127.34: 1\n", "25.95: 1\n", "20.86: 1\n", "138.28: 1\n", "23.56: 1\n", "72.78: 1\n", "212.83: 1\n", "139.49: 1\n", "10.8: 1\n", "16.11: 1\n", "58.52: 1\n", "93.78: 1\n", "158.12: 1\n", "133.53: 1\n", "152.14: 1\n", "242.91: 1\n", "121.23: 1\n", "207.47: 1\n", "232.96: 1\n", "30.32: 1\n", "67.27: 1\n", "174.21: 1\n", "14.72: 1\n", "7.51: 1\n", "26.65: 1\n", "12.62: 1\n", "73.11: 1\n", "12.66: 1\n", "245.96: 1\n", "123.59: 1\n", "16.99: 1\n", "69.93: 1\n", "6.57: 1\n", "127.24: 1\n", "43.43: 1\n", "97.18: 1\n", "9.46: 1\n", "153.43: 1\n", "314.53: 1\n", "22.78: 1\n", "23.92: 1\n", "130.39: 1\n", "17.41: 1\n", "12.22: 1\n", "72.16: 1\n", "175.97: 1\n", "59.52: 1\n", "184.0: 1\n", "61.8: 1\n", "71.34: 1\n", "8.87: 1\n", "72.44: 1\n", "87.42: 1\n", "382.34: 1\n", "270.66: 1\n", "44.67: 1\n", "38.87: 1\n", "115.17: 1\n", "9.39: 1\n", "102.55: 2\n", "289.85: 1\n", "143.28: 1\n", "75.9: 1\n", "93.94: 1\n", "287.6: 1\n", "156.01: 1\n", "1.47: 1\n", "79.11: 1\n", "159.34: 1\n", "97.61: 1\n", "5.48: 1\n", "13.15: 1\n", "187.62: 1\n", "230.61: 1\n", "123.8: 1\n", "126.0: 1\n", "194.41: 1\n", "151.34: 1\n", "37.9: 1\n", "119.49: 1\n", "11.48: 1\n", "344.24: 1\n", "58.94: 1\n", "179.28: 1\n", "225.48: 1\n", "46.83: 1\n", "128.46: 1\n", "201.35: 1\n", "173.02: 1\n", "186.16: 1\n", "4.55: 1\n", "11.83: 1\n", "6.69: 1\n", "189.84: 1\n", "86.0: 1\n", "287.38: 1\n", "136.76: 1\n", "101.55: 1\n", "195.31: 1\n", "244.64: 1\n", "46.48: 1\n", "14.88: 1\n", "54.58: 1\n", "153.32: 1\n", "92.14: 1\n", "81.83: 1\n", "5.68: 1\n", "96.58: 1\n", "136.11: 1\n", "95.93: 1\n", "294.7: 1\n", "11.82: 1\n", "237.64: 1\n", "261.91: 1\n", "99.73: 1\n", "183.85: 1\n", "93.69: 1\n", "130.21: 1\n", "164.79: 1\n", "285.0: 1\n", "262.33: 1\n", "357.45: 1\n", "169.38: 1\n", "106.34: 1\n", "147.41: 1\n", "99.95: 1\n", "156.56: 1\n", "90.49: 1\n", "168.85: 1\n", "206.77: 1\n", "228.23: 1\n", "198.95: 1\n", "137.64: 1\n", "75.25: 1\n", "95.37: 1\n", "275.38: 1\n", "171.63: 1\n", "205.98: 1\n", "212.1: 1\n", "251.49: 1\n", "105.99: 1\n", "65.01: 1\n", "177.49: 1\n", "173.76: 1\n", "79.61: 1\n", "104.07: 1\n", "192.49: 1\n", "97.85: 1\n", "43.44: 1\n", "172.91: 1\n", "98.02: 1\n", "114.12: 1\n", "109.68: 1\n", "70.82: 1\n", "219.05: 1\n", "330.81: 1\n", "103.06: 1\n", "158.69: 1\n", "87.12: 1\n", "144.35: 1\n", "47.57: 1\n", "211.33: 1\n", "133.22: 1\n", "90.8: 1\n", "183.46: 1\n", "55.2: 1\n", "63.7: 1\n", "76.51: 1\n", "51.29: 1\n", "314.89: 1\n", "105.71: 1\n", "74.19: 1\n", "266.26: 1\n", "75.7: 1\n", "182.48: 1\n", "41.11: 1\n", "187.54: 1\n", "58.68: 1\n", "125.8: 1\n", "228.81: 1\n", "130.5: 1\n", "109.7: 1\n", "136.43: 1\n", "300.24: 1\n", "195.7: 1\n", "83.3: 1\n", "311.72: 1\n", "34.76: 1\n", "49.34: 1\n", "170.31: 1\n", "76.62: 1\n", "163.8: 1\n", "230.98: 1\n", "276.4: 1\n", "343.7: 1\n", "324.04: 1\n", "133.5: 1\n", "146.75: 1\n", "160.11: 1\n", "67.5: 1\n", "227.99: 1\n", "165.69: 1\n", "97.36: 1\n", "59.85: 1\n", "95.61: 1\n", "140.78: 1\n", "164.72: 1\n", "65.77: 1\n", "75.13: 1\n", "28.8: 1\n", "20.6: 1\n", "84.24: 1\n", "266.46: 1\n", "105.21: 1\n", "253.39: 1\n", "219.59: 1\n", "87.39: 1\n", "214.83: 1\n", "55.63: 1\n", "76.38: 1\n", "91.54: 1\n", "96.4: 1\n", "252.38: 1\n", "95.67: 1\n", "137.47: 1\n", "35.11: 1\n", "259.11: 1\n", "147.3: 1\n", "100.95: 1\n", "168.26: 1\n", "73.58: 1\n", "132.67: 1\n", "58.28: 1\n", "106.78: 1\n", "68.16: 1\n", "180.53: 1\n", "110.89: 1\n", "103.43: 1\n", "120.38: 1\n", "80.67: 1\n", "199.1: 1\n", "104.66: 1\n", "211.65: 1\n", "119.73: 1\n", "88.87: 2\n", "22.4: 1\n", "145.99: 1\n", "160.04: 1\n", "140.96: 1\n", "50.24: 1\n", "154.33: 1\n", "188.94: 1\n", "38.32: 1\n", "163.94: 1\n", "260.12: 1\n", "93.89: 1\n", "99.44: 1\n", "57.0: 1\n", "274.33: 1\n", "34.31: 1\n", "114.62: 1\n", "53.93: 1\n", "96.7: 1\n", "53.57: 1\n", "66.66: 1\n", "113.87: 1\n", "211.4: 1\n", "113.0: 1\n", "301.55: 1\n", "343.17: 1\n", "79.91: 1\n", "88.71: 1\n", "83.05: 1\n", "83.24: 1\n", "150.66: 1\n", "190.48: 1\n", "191.04: 1\n", "297.02: 1\n", "115.6: 1\n", "139.44: 1\n", "62.57: 1\n", "77.19: 1\n", "71.15: 1\n", "63.05: 1\n", "33.99: 1\n", "108.03: 1\n", "204.12: 1\n", "162.92: 1\n", "125.58: 1\n", "123.89: 1\n", "53.13: 1\n", "132.4: 1\n", "25.06: 1\n", "76.0: 1\n", "282.17: 1\n", "77.05: 1\n", "238.44: 1\n", "136.75: 1\n", "131.89: 1\n", "76.8: 1\n", "147.97: 1\n", "74.82: 1\n", "316.73: 1\n", "448.14: 2\n", "231.73: 1\n", "304.48: 1\n", "323.6: 1\n", "444.75: 1\n", "513.5: 1\n", "487.56: 1\n", "279.33: 1\n", "193.29: 1\n", "478.33: 1\n", "173.32: 1\n", "150.63: 1\n", "236.33: 1\n", "303.08: 1\n", "114.76: 1\n", "283.54: 1\n", "76.19: 1\n", "243.73: 1\n", "62.77: 1\n", "290.22: 1\n", "214.14: 1\n", "84.7: 1\n", "23.59: 1\n", "334.85: 1\n", "170.1: 1\n", "178.12: 1\n", "324.57: 1\n", "316.69: 1\n", "75.58: 1\n", "81.55: 1\n", "230.64: 1\n", "317.01: 1\n", "180.77: 1\n", "282.11: 1\n", "38.76: 1\n", "161.4: 1\n", "329.19: 1\n", "390.39: 1\n", "461.34: 1\n", "370.45: 1\n", "492.72: 1\n", "311.71: 1\n", "218.35: 1\n", "143.07: 1\n", "435.83: 1\n", "48.03: 1\n", "38.58: 1\n", "34.91: 1\n", "287.83: 1\n", "82.82: 1\n", "331.33: 1\n", "146.62: 1\n", "189.27: 1\n", "76.79: 1\n", "263.77: 1\n", "208.17: 1\n", "172.85: 1\n", "131.76: 1\n", "279.84: 1\n", "10.54: 1\n", "287.56: 1\n", "377.66: 1\n", "389.02: 1\n", "22.17: 1\n", "90.39: 1\n", "63.83: 1\n", "307.69: 1\n", "139.75: 1\n", "62.01: 1\n", "53.77: 1\n", "185.15: 1\n", "288.07: 1\n", "435.67: 1\n", "64.74: 1\n", "629.82: 1\n", "573.74: 1\n", "347.93: 1\n", "298.92: 1\n", "286.42: 1\n", "109.03: 1\n", "218.18: 1\n", "181.4: 1\n", "144.11: 1\n", "474.84: 1\n", "148.9: 1\n", "169.98: 1\n", "61.7: 1\n", "354.72: 1\n", "539.63: 1\n", "92.31: 1\n", "507.88: 1\n", "79.01: 1\n", "50.15: 1\n", "245.41: 1\n", "226.73: 1\n", "340.99: 1\n", "353.82: 1\n", "209.36: 1\n", "235.94: 1\n", "84.62: 1\n", "324.89: 1\n", "367.58: 1\n", "18.59: 1\n", "44.22: 1\n", "451.19: 1\n", "253.22: 1\n", "45.75: 1\n", "194.85: 1\n", "277.15: 1\n", "42.69: 1\n", "277.21: 1\n", "106.47: 1\n", "288.43: 1\n", "169.72: 1\n", "93.1: 1\n", "140.82: 1\n", "271.94: 1\n", "253.9: 1\n", "256.6: 1\n", "477.25: 1\n", "179.07: 1\n", "134.98: 1\n", "778.49: 1\n", "221.15: 1\n", "104.54: 1\n", "489.72: 1\n", "474.78: 1\n", "183.98: 1\n", "131.95: 1\n", "228.54: 1\n", "305.91: 1\n", "308.21: 1\n", "38.27: 1\n", "206.02: 1\n", "317.23: 1\n", "273.11: 1\n", "276.37: 1\n", "52.79: 2\n", "328.02: 1\n", "78.19: 1\n", "125.93: 1\n", "343.48: 1\n", "129.83: 1\n", "257.54: 1\n", "217.88: 1\n", "112.96: 1\n", "91.3: 1\n", "361.93: 1\n", "92.59: 1\n", "833.67: 1\n", "148.83: 1\n", "178.93: 1\n", "151.52: 1\n", "144.03: 1\n", "74.24: 1\n", "384.08: 1\n", "527.94: 1\n", "68.91: 1\n", "178.3: 1\n", "39.86: 1\n", "175.67: 1\n", "217.79: 1\n", "117.34: 1\n", "225.53: 1\n", "132.69: 1\n", "470.66: 1\n", "49.63: 1\n", "174.95: 1\n", "251.11: 1\n", "281.49: 1\n", "143.57: 1\n", "219.23: 1\n", "37.83: 1\n", "296.12: 1\n", "122.51: 1\n", "220.61: 1\n", "60.02: 1\n", "135.77: 1\n", "143.31: 1\n", "153.16: 1\n", "117.25: 1\n", "314.35: 1\n", "278.37: 1\n", "84.68: 1\n", "364.3: 1\n", "263.5: 1\n", "137.8: 1\n", "23.13: 1\n", "154.8: 1\n", "55.7: 1\n", "132.59: 1\n", "518.8: 1\n", "108.22: 1\n", "250.69: 1\n", "48.32: 1\n", "26.87: 1\n", "106.19: 1\n", "205.2: 1\n", "270.4: 1\n", "263.58: 1\n", "141.38: 1\n", "180.18: 1\n", "203.8: 1\n", "194.09: 1\n", "113.5: 1\n", "145.81: 1\n", "389.48: 1\n", "153.64: 1\n", "146.85: 1\n", "163.39: 1\n", "116.73: 1\n", "412.05: 1\n", "218.64: 1\n", "569.58: 1\n", "227.21: 1\n", "113.59: 1\n", "181.84: 1\n", "615.49: 1\n", "307.83: 1\n", "166.47: 1\n", "185.54: 1\n", "549.17: 1\n", "491.5: 1\n", "388.99: 1\n", "28.49: 1\n", "318.07: 1\n", "170.5: 1\n", "150.61: 1\n", "157.98: 1\n", "368.42: 1\n", "171.87: 1\n", "235.87: 1\n", "363.86: 1\n", "359.29: 1\n", "131.14: 1\n", "158.45: 1\n", "257.42: 1\n", "248.01: 1\n", "333.33: 1\n", "316.08: 1\n", "102.19: 1\n", "485.47: 1\n", "34.17: 1\n", "556.3: 1\n", "35.25: 1\n", "390.87: 1\n", "117.44: 1\n", "134.35: 1\n", "71.66: 1\n", "134.1: 1\n", "11.2: 1\n", "88.07: 1\n", "94.96: 1\n", "592.32: 1\n", "59.41: 1\n", "768.81: 1\n", "6.84: 1\n", "277.56: 1\n", "28.13: 1\n", "154.65: 1\n", "337.36: 1\n", "205.26: 1\n", "347.78: 1\n", "501.0: 1\n", "240.66: 1\n", "173.43: 1\n", "825.57: 1\n", "222.63: 1\n", "171.97: 1\n", "243.13: 1\n", "168.79: 1\n", "286.41: 1\n", "168.98: 1\n", "216.13: 1\n", "71.35: 1\n", "133.66: 1\n", "770.21: 1\n", "86.29: 1\n", "639.13: 1\n", "62.66: 1\n", "110.27: 1\n", "424.61: 1\n", "93.96: 1\n", "294.21: 1\n", "45.71: 1\n", "408.52: 1\n", "253.26: 1\n", "109.76: 1\n", "359.18: 1\n", "108.1: 1\n", "42.07: 1\n", "344.71: 1\n", "251.18: 1\n", "65.39: 1\n", "70.76: 1\n", "335.59: 1\n", "109.91: 1\n", "210.59: 1\n", "295.13: 1\n", "+==================================================+\n", "Number of unique values: 27\n", "Unique values in 'hardship_start_date': [nan 'Feb-2019' 'Oct-2018' 'Nov-2018' 'Sep-2018' 'Jan-2019' 'Dec-2018'\n", " 'Aug-2018' 'Jul-2018' 'May-2018' 'Sep-2017' 'Feb-2018' 'Dec-2017'\n", " 'Apr-2018' 'Aug-2017' 'Jan-2018' 'Mar-2018' 'Jun-2018' 'Oct-2017'\n", " 'Nov-2017' 'Jul-2017' 'Jun-2017' 'May-2017' 'Feb-2017' 'Apr-2017'\n", " 'Jan-2017' 'Mar-2017']\n", "--------------------------------------------------\n", "Count of each unique value:\n", "nan: 0\n", "Feb-2019: 297\n", "Oct-2018: 594\n", "Nov-2018: 420\n", "Sep-2018: 422\n", "Jan-2019: 431\n", "Dec-2018: 339\n", "Aug-2018: 463\n", "Jul-2018: 359\n", "May-2018: 216\n", "Sep-2017: 2444\n", "Feb-2018: 242\n", "Dec-2017: 359\n", "Apr-2018: 205\n", "Aug-2017: 360\n", "Jan-2018: 348\n", "Mar-2018: 238\n", "Jun-2018: 260\n", "Oct-2017: 1077\n", "Nov-2017: 466\n", "Jul-2017: 271\n", "Jun-2017: 400\n", "May-2017: 373\n", "Feb-2017: 11\n", "Apr-2017: 9\n", "Jan-2017: 3\n", "Mar-2017: 6\n", "+==================================================+\n", "Number of unique values: 28\n", "Unique values in 'hardship_end_date': [nan 'Apr-2019' 'Dec-2018' 'Jan-2019' 'Feb-2019' 'Oct-2018' 'May-2019'\n", " 'Mar-2019' 'Nov-2018' 'Aug-2018' 'Dec-2017' 'May-2018' 'Nov-2017'\n", " 'Mar-2018' 'Jul-2018' 'Apr-2018' 'Jun-2018' 'Sep-2017' 'Sep-2018'\n", " 'Oct-2017' 'Jan-2018' 'Feb-2018' 'Aug-2017' 'Jul-2017' 'Jun-2017'\n", " 'May-2017' 'Apr-2017' 'Mar-2017']\n", "--------------------------------------------------\n", "Count of each unique value:\n", "nan: 0\n", "Apr-2019: 367\n", "Dec-2018: 509\n", "Jan-2019: 518\n", "Feb-2019: 471\n", "Oct-2018: 397\n", "May-2019: 101\n", "Mar-2019: 315\n", "Nov-2018: 413\n", "Aug-2018: 266\n", "Dec-2017: 1756\n", "May-2018: 253\n", "Nov-2017: 1325\n", "Mar-2018: 356\n", "Jul-2018: 212\n", "Apr-2018: 295\n", "Jun-2018: 210\n", "Sep-2017: 368\n", "Sep-2018: 299\n", "Oct-2017: 396\n", "Jan-2018: 749\n", "Feb-2018: 401\n", "Aug-2017: 386\n", "Jul-2017: 174\n", "Jun-2017: 50\n", "May-2017: 16\n", "Apr-2017: 6\n", "Mar-2017: 4\n", "+==================================================+\n", "Number of unique values: 27\n", "Unique values in 'payment_plan_start_date': [nan 'Feb-2019' 'Oct-2018' 'Nov-2018' 'Dec-2018' 'Jan-2019' 'Sep-2018'\n", " 'Mar-2019' 'Aug-2018' 'Jun-2018' 'Sep-2017' 'Oct-2017' 'Feb-2018'\n", " 'Dec-2017' 'May-2018' 'Aug-2017' 'Jan-2018' 'Mar-2018' 'Apr-2018'\n", " 'Nov-2017' 'Jul-2018' 'Jul-2017' 'Jun-2017' 'May-2017' 'Mar-2017'\n", " 'Feb-2017' 'Apr-2017']\n", "--------------------------------------------------\n", "Count of each unique value:\n", "nan: 0\n", "Feb-2019: 387\n", "Oct-2018: 538\n", "Nov-2018: 481\n", "Dec-2018: 379\n", "Jan-2019: 368\n", "Sep-2018: 416\n", "Mar-2019: 102\n", "Aug-2018: 456\n", "Jun-2018: 231\n", "Sep-2017: 1715\n", "Oct-2017: 1629\n", "Feb-2018: 307\n", "Dec-2017: 413\n", "May-2018: 216\n", "Aug-2017: 294\n", "Jan-2018: 329\n", "Mar-2018: 218\n", "Apr-2018: 218\n", "Nov-2017: 640\n", "Jul-2018: 297\n", "Jul-2017: 343\n", "Jun-2017: 394\n", "May-2017: 217\n", "Mar-2017: 11\n", "Feb-2017: 9\n", "Apr-2017: 5\n", "+==================================================+\n", "Number of unique values: 2\n", "Unique values in 'hardship_length': [nan  3.]\n", "--------------------------------------------------\n", "Count of each unique value:\n", "nan: 0\n", "3.0: 10613\n", "+==================================================+\n", "Number of unique values: 35\n", "Unique values in 'hardship_dpd': [nan 22.  0. 18. 23. 28. 29.  7. 16. 11.  6. 13.  2. 14. 10. 19.  9.  5.\n", " 21. 26. 24. 12.  3. 17.  4.  8. 20. 27. 15.  1. 25. 32. 30. 37. 31.]\n", "--------------------------------------------------\n", "Count of each unique value:\n", "nan: 0\n", "22.0: 357\n", "0.0: 2402\n", "18.0: 337\n", "23.0: 410\n", "28.0: 349\n", "29.0: 268\n", "7.0: 312\n", "16.0: 372\n", "11.0: 359\n", "6.0: 152\n", "13.0: 328\n", "2.0: 49\n", "14.0: 273\n", "10.0: 293\n", "19.0: 335\n", "9.0: 269\n", "5.0: 117\n", "21.0: 350\n", "26.0: 409\n", "24.0: 330\n", "12.0: 306\n", "3.0: 61\n", "17.0: 359\n", "4.0: 78\n", "8.0: 229\n", "20.0: 380\n", "27.0: 361\n", "15.0: 284\n", "1.0: 43\n", "25.0: 405\n", "32.0: 4\n", "30.0: 29\n", "37.0: 1\n", "31.0: 2\n", "+==================================================+\n", "Number of unique values: 6\n", "Unique values in 'hardship_loan_status': [nan 'Late (16-30 days)' 'Issued' 'Current' 'Late (31-120 days)'\n", " 'In Grace Period']\n", "--------------------------------------------------\n", "Count of each unique value:\n", "nan: 0\n", "Late (16-30 days): 4622\n", "Issued: 15\n", "Current: 2737\n", "Late (31-120 days): 433\n", "In Grace Period: 2806\n", "+==================================================+\n"]}], "source": ["# print(df[\"hardship_start_date\"].unique())\n", "# print(df[\"hardship_end_date\"].unique())\n", "\n", "columns = [\"hardship_flag\", \"hardship_type\", \"hardship_reason\", \"hardship_status\", \"deferral_term\", \"hardship_amount\", \"hardship_start_date\", \"hardship_end_date\", \"payment_plan_start_date\", \"hardship_length\", \"hardship_dpd\", \"hardship_loan_status\"]\n", "\n", "for col in columns:\n", "    unique_values = df[col].unique()\n", "    print(f\"Number of unique values: {len(unique_values)}\") \n", "    print(f\"Unique values in '{col}': {unique_values}\")\n", "    print('-'*50)\n", "    print(f\"Count of each unique value:\")\n", "    for unique_value in unique_values:\n", "        count = (df[col] == unique_value).sum()\n", "        print(f\"{unique_value}: {count}\")\n", "\n", "    print(\"+\" + \"=\"*50 + \"+\")"]}, {"cell_type": "code", "execution_count": 51, "id": "0ac01664", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['Current', 'Fully Paid', 'Late (31-120 days)', 'In Grace Period',\n", "       'Charged Off', 'Late (16-30 days)', 'Default',\n", "       'Does not meet the credit policy. Status:Fully Paid',\n", "       'Does not meet the credit policy. Status:Charged Off'],\n", "      dtype=object)"]}, "execution_count": 51, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"loan_status\"].unique()"]}, {"cell_type": "code", "execution_count": 48, "id": "24849959", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total columns: 145\n", "\n", "Column names at problematic indices:\n", "Column 123: hardship_type\n", "Column 124: hardship_reason\n", "Column 125: hardship_status\n", "Column 128: hardship_start_date\n", "Column 129: hardship_end_date\n", "Column 130: payment_plan_start_date\n", "Column 133: hardship_loan_status\n", "Column 139: debt_settlement_flag_date\n", "Column 140: settlement_status\n", "Column 141: settlement_date\n", "\n", "==================================================\n", "EXAMINING <PERSON>XED TYPE COLUMNS\n", "==================================================\n", "\n", "Sample data shape: (1000, 145)\n", "\n", "Column 123 - 'hardship_type':\n", "  Data type: float64\n", "  Unique values (first 10): [nan]\n", "  Non-null count: 0/1000\n", "\n", "Column 124 - 'hardship_reason':\n", "  Data type: float64\n", "  Unique values (first 10): [nan]\n", "  Non-null count: 0/1000\n", "\n", "Column 125 - 'hardship_status':\n", "  Data type: float64\n", "  Unique values (first 10): [nan]\n", "  Non-null count: 0/1000\n", "\n", "Column 128 - 'hardship_start_date':\n", "  Data type: float64\n", "  Unique values (first 10): [nan]\n", "  Non-null count: 0/1000\n", "\n", "Column 129 - 'hardship_end_date':\n", "  Data type: float64\n", "  Unique values (first 10): [nan]\n", "  Non-null count: 0/1000\n", "\n", "Column 130 - 'payment_plan_start_date':\n", "  Data type: float64\n", "  Unique values (first 10): [nan]\n", "  Non-null count: 0/1000\n", "\n", "Column 133 - 'hardship_loan_status':\n", "  Data type: float64\n", "  Unique values (first 10): [nan]\n", "  Non-null count: 0/1000\n", "\n", "Column 139 - 'debt_settlement_flag_date':\n", "  Data type: float64\n", "  Unique values (first 10): [nan]\n", "  Non-null count: 0/1000\n", "\n", "Column 140 - 'settlement_status':\n", "  Data type: float64\n", "  Unique values (first 10): [nan]\n", "  Non-null count: 0/1000\n", "\n", "Column 141 - 'settlement_date':\n", "  Data type: float64\n", "  Unique values (first 10): [nan]\n", "  Non-null count: 0/1000\n"]}], "source": ["# Let's first examine the problematic columns mentioned in the warning\n", "# Columns (123,124,125,128,129,130,133,139,140,141) have mixed types\n", "\n", "# First, let's read just the header to see column names\n", "# header_df = pd.DataFrame(df.iloc[0, :])\n", "header_df = pd.read_csv(DATA_PATH, nrows=0)\n", "print(f\"Total columns: {len(header_df.columns)}\")\n", "print(\"\\nColumn names at problematic indices:\")\n", "problematic_indices = [123, 124, 125, 128, 129, 130, 133, 139, 140, 141]\n", "for idx in problematic_indices:\n", "    if idx < len(header_df.columns):\n", "        print(f\"Column {idx}: {header_df.columns[idx]}\")\n", "    else:\n", "        print(f\"Column {idx}: Index out of range\")\n", "\n", "# Now let's examine a small sample to see the mixed types\n", "print(\"\\n\" + \"=\"*50)\n", "print(\"EXAMINING MIXED TYPE COLUMNS\")\n", "print(\"=\"*50)\n", "\n", "# Read a small sample to inspect the data types\n", "sample_df = pd.read_csv(DATA_PATH, nrows=1000, low_memory=False)\n", "print(f\"\\nSample data shape: {sample_df.shape}\")\n", "\n", "# Check the problematic columns\n", "for idx in problematic_indices:\n", "    if idx < len(sample_df.columns):\n", "        col_name = sample_df.columns[idx]\n", "        print(f\"\\nColumn {idx} - '{col_name}':\")\n", "        print(f\"  Data type: {sample_df[col_name].dtype}\")\n", "        print(f\"  Unique values (first 10): {sample_df[col_name].unique()[:10]}\")\n", "        print(f\"  Non-null count: {sample_df[col_name].count()}/{len(sample_df)}\")"]}, {"cell_type": "code", "execution_count": null, "id": "c4a560aa", "metadata": {}, "outputs": [], "source": ["features = [\n", "    'loan_amnt',\n", "    'int_rate',\n", "    'installment',\n", "    'grade',\n", "    'sub_grade',\n", "    'annual_inc',\n", "    'dti',\n", "    'fico_range_low',\n", "    'fico_range_high',\n", "    'revol_util',\n", "    'revol_bal',\n", "    'emp_length',\n", "    'home_ownership',\n", "    'verification_status',\n", "    'purpose',\n", "    'term',\n", "    'open_acc',\n", "    'total_acc',\n", "    'inq_last_6mths',\n", "    'delinq_2yrs',\n", "    'pub_rec',\n", "    'pub_rec_bankruptcies',\n", "    'mths_since_last_delinq',\n", "    'acc_open_past_24mths',\n", "    'mort_acc',\n", "    'avg_cur_bal',\n", "    'bc_util',\n", "    'tot_hi_cred_lim',\n", "    'mo_sin_old_rev_tl_op',\n", "    'num_actv_rev_tl'\n", "]"]}, {"cell_type": "code", "execution_count": 2, "id": "9409665d", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'df' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[2], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m \u001b[43mdf\u001b[49m[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mloan_status\u001b[39m\u001b[38;5;124m\"\u001b[39m]\u001b[38;5;241m.\u001b[39munique()\n", "\u001b[1;31mNameError\u001b[0m: name 'df' is not defined"]}], "source": ["df[\"loan_status\"].unique()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.10"}}, "nbformat": 4, "nbformat_minor": 5}