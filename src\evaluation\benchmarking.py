"""
Benchmarking and evaluation system for loan approval predictions.
"""

import time
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import pandas as pd
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score, confusion_matrix
import numpy as np

from ..models.loan_data import LoanData
from ..models.llm_response import LLMResponse, EvaluationResult, BatchEvaluationResult, ComparisonResult, LoanDecision
from ..approaches.base import LoanApprovalApproach


class LoanApprovalEvaluator:
    """Evaluator for loan approval prediction approaches."""
    
    def __init__(self):
        self.evaluation_history = []
    
    def evaluate_single_prediction(self, 
                                 prediction: LLMResponse, 
                                 actual_outcome: bool,
                                 loan_id: Optional[str] = None) -> EvaluationResult:
        """
        Evaluate a single prediction against ground truth.
        
        Args:
            prediction: LLM prediction response
            actual_outcome: True if loan was good (fully paid/current), False if bad
            loan_id: Optional loan identifier
            
        Returns:
            EvaluationResult with correctness metrics
        """
        # Convert LLM decision to boolean
        predicted_approve = prediction.decision == LoanDecision.APPROVE
        
        # Determine correctness
        is_correct = predicted_approve == actual_outcome
        
        # Classify prediction type
        if predicted_approve and actual_outcome:
            prediction_type = "TP"  # True Positive
        elif not predicted_approve and not actual_outcome:
            prediction_type = "TN"  # True Negative
        elif predicted_approve and not actual_outcome:
            prediction_type = "FP"  # False Positive
        else:  # not predicted_approve and actual_outcome
            prediction_type = "FN"  # False Negative
        
        return EvaluationResult(
            predicted_decision=prediction.decision,
            actual_outcome=actual_outcome,
            is_correct=is_correct,
            prediction_type=prediction_type,
            confidence_level=prediction.confidence,
            risk_level=prediction.risk_assessment,
            loan_id=loan_id,
            approach_used=prediction.model_name or "Unknown"
        )
    
    def evaluate_batch_predictions(self, 
                                 predictions: List[LLMResponse],
                                 actual_outcomes: List[bool],
                                 approach_name: str,
                                 loan_ids: Optional[List[str]] = None) -> BatchEvaluationResult:
        """
        Evaluate a batch of predictions against ground truth.
        
        Args:
            predictions: List of LLM prediction responses
            actual_outcomes: List of actual loan outcomes
            approach_name: Name of the approach being evaluated
            loan_ids: Optional list of loan identifiers
            
        Returns:
            BatchEvaluationResult with comprehensive metrics
        """
        start_time = time.time()
        
        if len(predictions) != len(actual_outcomes):
            raise ValueError("Number of predictions must match number of actual outcomes")
        
        if loan_ids and len(loan_ids) != len(predictions):
            raise ValueError("Number of loan IDs must match number of predictions")
        
        # Evaluate individual predictions
        individual_results = []
        for i, (prediction, actual) in enumerate(zip(predictions, actual_outcomes)):
            loan_id = loan_ids[i] if loan_ids else None
            result = self.evaluate_single_prediction(prediction, actual, loan_id)
            individual_results.append(result)
        
        # Calculate aggregate metrics
        predicted_approvals = [pred.decision == LoanDecision.APPROVE for pred in predictions]
        
        # Basic metrics
        accuracy = accuracy_score(actual_outcomes, predicted_approvals)
        precision = precision_score(actual_outcomes, predicted_approvals, zero_division=0)
        recall = recall_score(actual_outcomes, predicted_approvals, zero_division=0)
        f1 = f1_score(actual_outcomes, predicted_approvals, zero_division=0)
        
        # Confusion matrix
        tn, fp, fn, tp = confusion_matrix(actual_outcomes, predicted_approvals).ravel()
        
        # Count correct predictions
        correct_predictions = sum(1 for result in individual_results if result.is_correct)
        total_predictions = len(predictions)
        
        evaluation_duration = time.time() - start_time
        
        return BatchEvaluationResult(
            total_predictions=total_predictions,
            correct_predictions=correct_predictions,
            accuracy=accuracy,
            true_positives=int(tp),
            true_negatives=int(tn),
            false_positives=int(fp),
            false_negatives=int(fn),
            precision=precision,
            recall=recall,
            f1_score=f1,
            approach_name=approach_name,
            individual_results=individual_results,
            evaluation_duration=evaluation_duration
        )
    
    def compare_approaches(self, 
                          batch_results: List[BatchEvaluationResult],
                          primary_metric: str = "f1_score") -> ComparisonResult:
        """
        Compare multiple approaches and rank them.
        
        Args:
            batch_results: List of batch evaluation results
            primary_metric: Primary metric for ranking (accuracy, precision, recall, f1_score)
            
        Returns:
            ComparisonResult with rankings and comparisons
        """
        if not batch_results:
            raise ValueError("No batch results provided for comparison")
        
        # Create results dictionary
        approach_results = {result.approach_name: result for result in batch_results}
        
        # Rank approaches by different metrics
        metrics = ["accuracy", "precision", "recall", "f1_score"]
        rankings = {}
        
        for metric in metrics:
            metric_values = [(name, getattr(result, metric)) for name, result in approach_results.items()]
            metric_values.sort(key=lambda x: x[1], reverse=True)
            rankings[f"{metric}_ranking"] = [name for name, _ in metric_values]
        
        # Determine best approach based on primary metric
        primary_values = [(name, getattr(result, primary_metric)) for name, result in approach_results.items()]
        best_approach = max(primary_values, key=lambda x: x[1])[0]
        
        return ComparisonResult(
            approach_results=approach_results,
            best_approach=best_approach,
            best_metric=primary_metric,
            accuracy_ranking=rankings["accuracy_ranking"],
            precision_ranking=rankings["precision_ranking"],
            recall_ranking=rankings["recall_ranking"],
            f1_ranking=rankings["f1_score_ranking"]
        )
    
    def calculate_advanced_metrics(self, 
                                 predictions: List[LLMResponse],
                                 actual_outcomes: List[bool]) -> Dict[str, float]:
        """
        Calculate advanced evaluation metrics.
        
        Args:
            predictions: List of LLM prediction responses
            actual_outcomes: List of actual loan outcomes
            
        Returns:
            Dictionary with advanced metrics
        """
        predicted_approvals = [pred.decision == LoanDecision.APPROVE for pred in predictions]
        
        # Get probability scores if available
        approval_probs = []
        for pred in predictions:
            if pred.approval_probability is not None:
                approval_probs.append(pred.approval_probability)
            else:
                # Use confidence as proxy
                confidence_to_prob = {
                    "VERY_LOW": 0.1,
                    "LOW": 0.3,
                    "MEDIUM": 0.5,
                    "HIGH": 0.7,
                    "VERY_HIGH": 0.9
                }
                prob = confidence_to_prob.get(pred.confidence.value, 0.5)
                if pred.decision == LoanDecision.DENY:
                    prob = 1.0 - prob
                approval_probs.append(prob)
        
        metrics = {}
        
        # ROC AUC if we have probability scores
        if approval_probs:
            try:
                metrics["roc_auc"] = roc_auc_score(actual_outcomes, approval_probs)
            except ValueError:
                metrics["roc_auc"] = 0.5  # Random performance
        
        # Business metrics
        tn, fp, fn, tp = confusion_matrix(actual_outcomes, predicted_approvals).ravel()
        
        # False Positive Rate (approving bad loans)
        metrics["false_positive_rate"] = fp / (fp + tn) if (fp + tn) > 0 else 0
        
        # False Negative Rate (denying good loans)
        metrics["false_negative_rate"] = fn / (fn + tp) if (fn + tp) > 0 else 0
        
        # Specificity (correctly identifying bad loans)
        metrics["specificity"] = tn / (tn + fp) if (tn + fp) > 0 else 0
        
        # Negative Predictive Value
        metrics["negative_predictive_value"] = tn / (tn + fn) if (tn + fn) > 0 else 0
        
        # Matthews Correlation Coefficient
        denominator = np.sqrt((tp + fp) * (tp + fn) * (tn + fp) * (tn + fn))
        if denominator != 0:
            metrics["matthews_correlation"] = (tp * tn - fp * fn) / denominator
        else:
            metrics["matthews_correlation"] = 0
        
        return metrics
    
    def analyze_confidence_calibration(self, 
                                     predictions: List[LLMResponse],
                                     actual_outcomes: List[bool]) -> Dict[str, Any]:
        """
        Analyze how well-calibrated the confidence levels are.
        
        Args:
            predictions: List of LLM prediction responses
            actual_outcomes: List of actual loan outcomes
            
        Returns:
            Dictionary with calibration analysis
        """
        confidence_analysis = {}
        
        # Group by confidence level
        confidence_groups = {}
        for pred, actual in zip(predictions, actual_outcomes):
            conf_level = pred.confidence.value
            if conf_level not in confidence_groups:
                confidence_groups[conf_level] = {"correct": 0, "total": 0}
            
            confidence_groups[conf_level]["total"] += 1
            if (pred.decision == LoanDecision.APPROVE) == actual:
                confidence_groups[conf_level]["correct"] += 1
        
        # Calculate accuracy for each confidence level
        for conf_level, data in confidence_groups.items():
            accuracy = data["correct"] / data["total"] if data["total"] > 0 else 0
            confidence_analysis[conf_level] = {
                "accuracy": accuracy,
                "count": data["total"],
                "correct": data["correct"]
            }
        
        return confidence_analysis
    
    def generate_evaluation_report(self, 
                                 batch_result: BatchEvaluationResult,
                                 advanced_metrics: Optional[Dict[str, float]] = None,
                                 confidence_analysis: Optional[Dict[str, Any]] = None) -> str:
        """
        Generate a comprehensive evaluation report.
        
        Args:
            batch_result: Batch evaluation result
            advanced_metrics: Optional advanced metrics
            confidence_analysis: Optional confidence calibration analysis
            
        Returns:
            Formatted evaluation report
        """
        report = f"""
# Loan Approval Prediction Evaluation Report

## Approach: {batch_result.approach_name}
**Evaluation Date:** {batch_result.evaluation_timestamp.strftime('%Y-%m-%d %H:%M:%S')}
**Processing Time:** {batch_result.evaluation_duration:.2f} seconds

## Summary Metrics
- **Total Predictions:** {batch_result.total_predictions}
- **Correct Predictions:** {batch_result.correct_predictions}
- **Overall Accuracy:** {batch_result.accuracy:.3f} ({batch_result.accuracy*100:.1f}%)

## Performance Metrics
- **Precision:** {batch_result.precision:.3f}
- **Recall:** {batch_result.recall:.3f}
- **F1 Score:** {batch_result.f1_score:.3f}

## Confusion Matrix
|                | Predicted Deny | Predicted Approve |
|----------------|----------------|-------------------|
| **Actual Bad** | {batch_result.true_negatives:,} (TN)      | {batch_result.false_positives:,} (FP)       |
| **Actual Good**| {batch_result.false_negatives:,} (FN)      | {batch_result.true_positives:,} (TP)        |

## Business Impact
- **False Positive Rate:** {(batch_result.false_positives / (batch_result.false_positives + batch_result.true_negatives) * 100):.1f}% (Bad loans approved)
- **False Negative Rate:** {(batch_result.false_negatives / (batch_result.false_negatives + batch_result.true_positives) * 100):.1f}% (Good loans denied)
"""
        
        if advanced_metrics:
            report += f"""
## Advanced Metrics
- **ROC AUC:** {advanced_metrics.get('roc_auc', 'N/A'):.3f}
- **Specificity:** {advanced_metrics.get('specificity', 'N/A'):.3f}
- **Matthews Correlation:** {advanced_metrics.get('matthews_correlation', 'N/A'):.3f}
"""
        
        if confidence_analysis:
            report += "\n## Confidence Calibration\n"
            for conf_level, data in confidence_analysis.items():
                report += f"- **{conf_level}:** {data['accuracy']:.3f} accuracy ({data['correct']}/{data['count']} correct)\n"
        
        return report.strip()


class BenchmarkSuite:
    """Suite for running comprehensive benchmarks."""
    
    def __init__(self, evaluator: LoanApprovalEvaluator):
        self.evaluator = evaluator
        self.benchmark_history = []
    
    def run_comprehensive_benchmark(self, 
                                  approaches: List[Tuple[str, LoanApprovalApproach]],
                                  test_data: List[LoanData],
                                  ground_truth: List[bool]) -> ComparisonResult:
        """
        Run comprehensive benchmark across multiple approaches.
        
        Args:
            approaches: List of (name, approach) tuples
            test_data: List of loan data for testing
            ground_truth: List of actual loan outcomes
            
        Returns:
            ComparisonResult with comprehensive comparison
        """
        batch_results = []
        
        for approach_name, approach in approaches:
            print(f"Evaluating {approach_name}...")
            
            # Get predictions
            predictions = approach.predict_batch(test_data)
            
            # Evaluate predictions
            batch_result = self.evaluator.evaluate_batch_predictions(
                predictions, ground_truth, approach_name
            )
            
            batch_results.append(batch_result)
            
            # Calculate advanced metrics
            advanced_metrics = self.evaluator.calculate_advanced_metrics(predictions, ground_truth)
            
            # Analyze confidence calibration
            confidence_analysis = self.evaluator.analyze_confidence_calibration(predictions, ground_truth)
            
            # Generate report
            report = self.evaluator.generate_evaluation_report(
                batch_result, advanced_metrics, confidence_analysis
            )
            
            print(f"Completed evaluation for {approach_name}")
            print(f"Accuracy: {batch_result.accuracy:.3f}, F1: {batch_result.f1_score:.3f}")
        
        # Compare approaches
        comparison_result = self.evaluator.compare_approaches(batch_results)
        
        # Store benchmark history
        self.benchmark_history.append({
            "timestamp": datetime.now(),
            "comparison_result": comparison_result,
            "test_size": len(test_data)
        })
        
        return comparison_result
    
    def get_benchmark_summary(self) -> Dict[str, Any]:
        """Get summary of all benchmarks run."""
        if not self.benchmark_history:
            return {"message": "No benchmarks run yet"}
        
        latest_benchmark = self.benchmark_history[-1]
        comparison = latest_benchmark["comparison_result"]
        
        return {
            "total_benchmarks": len(self.benchmark_history),
            "latest_benchmark": {
                "timestamp": latest_benchmark["timestamp"],
                "test_size": latest_benchmark["test_size"],
                "best_approach": comparison.best_approach,
                "approaches_tested": list(comparison.approach_results.keys()),
                "performance_summary": comparison.get_performance_summary()
            }
        }
